
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Factory } from "lucide-react";
import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { getBrickTypes, type BrickType } from "@/data/fuelBunkersData";
import { useProductionEntries } from "@/hooks/useProductionEntries";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

interface FactoryOutputCardProps {
  onRecordProduction: () => void;
}

export const FactoryOutputCard = ({ onRecordProduction }: FactoryOutputCardProps) => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const { data: productionEntries = [] } = useProductionEntries();

  const { data: brickTypes = [] } = useQuery<BrickType[]>({
    queryKey: ['brickTypes'],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getBrickTypes(userId || undefined);
    },
  });

  const { pallets, bricks } = useMemo(() => {
    const brickTypeMap = new Map(brickTypes.map(bt => [bt.id, bt.bricks_per_pallet]));

    return productionEntries.reduce(
      (acc, entry) => {
        acc.pallets += entry.pallet_count;
        const bricksPerPallet = brickTypeMap.get(entry.brick_type_id) || 0;
        acc.bricks += entry.pallet_count * bricksPerPallet;
        return acc;
      },
      { pallets: 0, bricks: 0 }
    );
  }, [productionEntries, brickTypes]);

  return (
    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-orange-800 flex items-center gap-2">
          <Factory size={20} />
          Factory Output
        </CardTitle>
        <p className="text-sm text-orange-600">Live production tracking</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex gap-6">
            <div>
              <p className="text-sm text-orange-600">Bricks</p>
              <p className="text-2xl font-bold text-orange-800">{bricks.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm text-orange-600">Pallets</p>
              <p className="text-2xl font-bold text-orange-800">{pallets.toLocaleString()}</p>
            </div>
          </div>
          <Button 
            className="w-full bg-orange-500 hover:bg-orange-600"
            onClick={onRecordProduction}
          >
            Record Production
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
