
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { usePerformanceAnalytics } from '@/hooks/usePerformanceAnalytics';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { Users, Trophy, TrendingUp, AlertCircle, Award, Target } from 'lucide-react';
import { toast } from 'sonner';

export const PerformanceAnalytics = () => {
  const [activeTab, setActiveTab] = useState('teams');
  const { data: analytics, isLoading } = usePerformanceAnalytics();

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const getPerformanceBadge = (score: number) => {
    if (score >= 90) return <Badge className="bg-green-500">Excellent</Badge>;
    if (score >= 80) return <Badge className="bg-blue-500">Good</Badge>;
    if (score >= 70) return <Badge className="bg-yellow-500">Average</Badge>;
    return <Badge variant="destructive">Needs Improvement</Badge>;
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Target className="h-4 w-4 text-blue-500" />;
    }
  };

  const handleReviewRecommendation = (recommendationIndex: number, recommendation: string) => {
    toast.success(`Reviewing recommendation ${recommendationIndex + 1}`, {
      description: "Performance recommendation has been marked for review"
    });
    console.log(`Reviewing recommendation ${recommendationIndex + 1}:`, recommendation);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Loading Performance Analytics...</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">Analyzing your team and employee data...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Performance Analytics
          </CardTitle>
          <CardDescription>
            AI-driven insights into team and individual performance across your operations
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Performance Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="teams">Team Performance</TabsTrigger>
          <TabsTrigger value="individuals">Top Performers</TabsTrigger>
          <TabsTrigger value="departments">Department Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="teams" className="space-y-6">
          {/* Team Performance Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Team Performance Analysis</CardTitle>
              <CardDescription>Based on actual production data and team activities</CardDescription>
            </CardHeader>
            <CardContent>
              {analytics?.teams && analytics.teams.length > 0 ? (
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analytics.teams}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="performance" fill="#8884d8" name="Performance Score" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No team performance data available. Teams will appear here based on production entries and activities.</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Team Details */}
          <Card>
            <CardHeader>
              <CardTitle>Team Performance Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics?.teams && analytics.teams.length > 0 ? analytics.teams.map((team, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Trophy className={`h-5 w-5 ${index === 0 ? 'text-yellow-500' : index === 1 ? 'text-gray-400' : index === 2 ? 'text-orange-500' : 'text-gray-300'}`} />
                        <span className="font-semibold">{team.name}</span>
                      </div>
                      {getTrendIcon(team.trend)}
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Performance</div>
                        <div className="font-medium">{team.performance}%</div>
                      </div>
                      {getPerformanceBadge(team.performance)}
                    </div>
                  </div>
                )) : (
                  <p className="text-muted-foreground text-center py-4">Team performance data will be calculated based on production activities.</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="individuals" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Top Individual Performers
              </CardTitle>
              <CardDescription>Based on actual employee activities and contributions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics?.individuals && analytics.individuals.length > 0 ? analytics.individuals.map((employee, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                          index === 0 ? 'bg-yellow-100 text-yellow-800' : 
                          index === 1 ? 'bg-gray-100 text-gray-800' : 
                          index === 2 ? 'bg-orange-100 text-orange-800' : 
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-semibold">{employee.employeeName || employee.name}</div>
                          <div className="text-sm text-muted-foreground">{employee.department}</div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-sm text-muted-foreground">Performance</div>
                        <div className="font-medium">{employee.performance}%</div>
                      </div>
                      {getPerformanceBadge(employee.performance)}
                    </div>
                  </div>
                )) : (
                  <p className="text-muted-foreground text-center py-4">Individual performance rankings will be generated from production data and employee activities.</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="departments" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Department Overview</CardTitle>
                <CardDescription>Performance across different departments</CardDescription>
              </CardHeader>
              <CardContent>
                {analytics?.departments && analytics.departments.length > 0 ? (
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={analytics.departments}
                          dataKey="performance"
                          nameKey="name"
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          label={(entry) => `${entry.name}: ${entry.performance}%`}
                        >
                          {analytics.departments.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Department analysis will be available once there is sufficient activity data.</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
                <CardDescription>Key insights from your performance data</CardDescription>
              </CardHeader>
              <CardContent>
                {analytics ? (
                  <div className="space-y-4">
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <p className="text-2xl font-bold">{analytics.dataPoints || 0}</p>
                      <p className="text-sm text-muted-foreground">Data points analyzed</p>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <p>Performance analysis includes:</p>
                      <ul className="mt-2 space-y-1 list-disc list-inside">
                        <li>Employee activity records</li>
                        <li>Team collaboration data</li>
                        <li>Production contributions</li>
                        <li>Quality metrics</li>
                      </ul>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Performance summary will be displayed here after data analysis.</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* AI Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Recommendations</CardTitle>
          <CardDescription>
            AI-generated suggestions for improving team and individual performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics?.recommendations && analytics.recommendations.length > 0 ? analytics.recommendations.map((rec, index) => (
              <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-semibold">Recommendation {index + 1}</h4>
                    <Badge variant="outline">
                      AI Generated
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{rec}</p>
                </div>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleReviewRecommendation(index, rec)}
                >
                  Review
                </Button>
              </div>
            )) : (
              <p className="text-muted-foreground text-center py-4">Performance recommendations will be generated based on your team's activity data.</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
