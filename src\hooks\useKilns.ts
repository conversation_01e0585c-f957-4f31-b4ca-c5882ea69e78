import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getKilns, updateKilnStatus, getFires } from "@/data/kilnData";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { toast } from "sonner";

export function useKilns() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["kilns"],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        console.error('No authenticated user found for kilns query');
        return [];
      }

      return getKilns(userId);
    },
  });
}

export function useFires() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["fires"],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        console.error('No authenticated user found for fires query');
        return [];
      }

      return getFires(userId);
    },
  });
}

export function useUpdateKilnStatus() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async ({ id, status }: { id: string; status: any }) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      
      return updateKilnStatus({ id, status, userId: userId || undefined });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kilns"] });
      toast.success("Kiln status updated successfully");
    },
    onError: (error: any) => {
      toast.error(`Failed to update kiln status: ${error.message}`);
    },
  });
}
