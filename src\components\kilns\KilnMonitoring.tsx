
import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Thermometer, Activity, Gauge, Clock, AlertTriangle, CheckCircle, Plus, Download, FileText, TrendingUp, Calendar } from "lucide-react";
import { toast } from "sonner";
import {
  useMeasurements,
  useCreateMeasurement,
  useChamberZones,
  useUpdateChamberZone,
  useInitializeChamberZones,
  useParameterNorms,
  useUpdateParameterNorm
} from "@/hooks/useKilnMonitoring";
import { EnhancedParameterNorms } from "./EnhancedParameterNorms";
import { KilnZoneOverview } from "./KilnZoneOverview";
import { SimplifiedDailySummary } from "./SimplifiedDailySummary";
import { ParameterTrends } from "./ParameterTrends";

interface MeasurementData {
  measurement_time: string;
  preheat_zone_1_temp: string;
  preheat_zone_2_temp: string;
  pre_combustion_zone_temp: string;
  fire_zone_temp: string;
  cooling_zone_1_temp: string;
  cooling_zone_2_temp: string;
  fire_position_chamber: string;
  fire_movement_meters: string;
  o2: string;
  co2: string;
  draught_pressure: string;
  fuel_to_brick_ratio_setting: string;
  brick_moisture_setting: string;
}

const KILN_OPTIONS = [
  { id: "habla-kiln", name: "Habla Kiln", chambers: 10 },
  { id: "kiln-1", name: "Kiln 1", chambers: 24 },
  { id: "kiln-2", name: "Kiln 2", chambers: 24 },
  { id: "kiln-3", name: "Kiln 3", chambers: 24 },
  { id: "kiln-4", name: "Kiln 4", chambers: 24 },
  { id: "kiln-5", name: "Kiln 5", chambers: 24 },
];

const ZONE_OPTIONS = ["Inactive", "Preheat 1", "Preheat 2", "Pre-combustion", "Firing", "Cooling 1", "Cooling 2", "Setting", "Dehacking"];

const ZONE_COLORS = {
  "Inactive": "#e2e8f0",
  "Preheat 1": "#93c5fd",
  "Preheat 2": "#60a5fa",
  "Pre-combustion": "#eab308",
  "Firing": "#f97316",
  "Cooling 1": "#dc2626",
  "Cooling 2": "#fca5a5",
  "Setting": "#16a34a",
  "Dehacking": "#fca5a5"
};

const PARAMETER_OPTIONS = [
  { id: "preheat_zone_1_temp", name: "Pre-heat Zone 1 Temp", unit: "°C" },
  { id: "preheat_zone_2_temp", name: "Pre-heat Zone 2 Temp", unit: "°C" },
  { id: "pre_combustion_zone_temp", name: "Pre-combustion Zone Temp", unit: "°C" },
  { id: "fire_zone_temp", name: "Fire Zone Temp", unit: "°C" },
  { id: "cooling_zone_1_temp", name: "Cooling Zone 1 Temp", unit: "°C" },
  { id: "cooling_zone_2_temp", name: "Cooling Zone 2 Temp", unit: "°C" },
  { id: "fire_position_chamber", name: "Fire Position in Chamber", unit: "m" },
  { id: "fire_movement_meters", name: "Fire Movement", unit: "Meters" },
  { id: "o2", name: "O₂", unit: "%" },
  { id: "co2", name: "CO₂", unit: "%" },
  { id: "draught_pressure", name: "Draught Pressure", unit: "mmWC" },
  { id: "fuel_to_brick_ratio_setting", name: "Fuel to Brick Ratio - Setting", unit: "%" },
  { id: "brick_moisture_setting", name: "Brick Moisture % - Setting", unit: "%" },
];

// Updated parameter norms data
const DEFAULT_PARAMETER_NORMS = [
  { parameter_name: "Pre-heat Zone 1 Temp", unit: "°C", min_value: 200, max_value: 350, cause: "Improper firing schedule", action: "Adjust heating rate" },
  { parameter_name: "Pre-heat Zone 2 Temp", unit: "°C", min_value: 350, max_value: 500, cause: "Improper firing schedule", action: "Adjust heating rate" },
  { parameter_name: "Pre-combustion Zone Temp", unit: "°C", min_value: 500, max_value: 700, cause: "Improper fuel supply", action: "Adjust fuel input" },
  { parameter_name: "Fire Zone Temp", unit: "°C", min_value: 850, max_value: 950, cause: "Improper fire control", action: "Adjust fire position" },
  { parameter_name: "Cooling Zone 1 Temp", unit: "°C", min_value: 200, max_value: 400, cause: "Improper cooling", action: "Adjust cooling rate" },
  { parameter_name: "Cooling Zone 2 Temp", unit: "°C", min_value: 50, max_value: 200, cause: "Improper cooling", action: "Adjust cooling fans" },
  { parameter_name: "Fire Position in Chamber", unit: "m", min_value: 0, max_value: 25, cause: "Fire control issues", action: "Monitor fire progression" },
  { parameter_name: "Fire Movement", unit: "Meters", min_value: 2.5, max_value: 3.75, cause: "Improper fire speed", action: "Adjust fire rate" },
  { parameter_name: "O₂", unit: "%", min_value: 3, max_value: 5, cause: "Improper air supply", action: "Adjust air intake" },
  { parameter_name: "CO₂", unit: "%", min_value: 12, max_value: 15, cause: "Inefficient combustion", action: "Tune air-fuel ratio" },
  { parameter_name: "Draught Pressure", unit: "mmWC", min_value: -8, max_value: -4, cause: "Blockage or leak", action: "Inspect fan/seals" },
  { parameter_name: "Fuel to Brick Ratio - Setting", unit: "%", min_value: 5, max_value: 6.5, cause: "Improper fuel calculation", action: "Recalculate fuel needs" },
  { parameter_name: "Brick Moisture % - Setting", unit: "%", min_value: 7.9, max_value: 10, cause: "Poor drying", action: "Improve loading/drying" },
];

export const KilnMonitoring = () => {
  const [selectedKiln, setSelectedKiln] = useState("habla-kiln");
  const [selectedChamber, setSelectedChamber] = useState(1);
  const [selectedParameter, setSelectedParameter] = useState("fire_zone_temp");
  const [measurementData, setMeasurementData] = useState<MeasurementData>({
    measurement_time: "08:00",
    preheat_zone_1_temp: "",
    preheat_zone_2_temp: "",
    pre_combustion_zone_temp: "",
    fire_zone_temp: "",
    cooling_zone_1_temp: "",
    cooling_zone_2_temp: "",
    fire_position_chamber: "",
    fire_movement_meters: "",
    o2: "",
    co2: "",
    draught_pressure: "",
    fuel_to_brick_ratio_setting: "",
    brick_moisture_setting: "",
  });

  const { data: measurements = [], refetch: refetchMeasurements } = useMeasurements({
    kiln_id: selectedKiln,
  });

  const { data: chamberZones = [], refetch: refetchZones, isLoading: zonesLoading, error: zonesError } = useChamberZones();

  // Debug logging
  useEffect(() => {
    console.log('🔍 Chamber zones data:', chamberZones);
    console.log('🔍 Zones loading:', zonesLoading);
    console.log('🔍 Zones error:', zonesError);
  }, [chamberZones, zonesLoading, zonesError]);
  
  const { data: parameterNorms = DEFAULT_PARAMETER_NORMS } = useParameterNorms();
  const createMeasurement = useCreateMeasurement();
  const updateChamberZone = useUpdateChamberZone();
  const initializeChamberZones = useInitializeChamberZones();
  const updateParameterNorm = useUpdateParameterNorm();

  const currentKiln = KILN_OPTIONS.find(k => k.id === selectedKiln);
  const maxChambers = currentKiln?.chambers || 24;

  useEffect(() => {
    const zonesForKiln = chamberZones.filter(z => z.kiln_id === selectedKiln);
    console.log(`🏭 Zones for ${selectedKiln}:`, zonesForKiln);
    console.log(`🏭 Expected chambers: ${maxChambers}, Found zones: ${zonesForKiln.length}`);

    if (zonesForKiln.length === 0 && !zonesLoading) {
      console.log(`🚀 Initializing chamber zones for ${selectedKiln}`);
      initializeChamberZones.mutate({
        kiln_id: selectedKiln,
        chamber_count: maxChambers
      }, {
        onSuccess: (data) => {
          console.log(`✅ Successfully initialized zones for ${selectedKiln}:`, data);
        },
        onError: (error) => {
          console.error(`❌ Failed to initialize zones for ${selectedKiln}:`, error);
        }
      });
    }
  }, [selectedKiln, chamberZones, maxChambers, zonesLoading]);

  const handleMeasurementChange = (field: keyof MeasurementData, value: string) => {
    setMeasurementData(prev => ({ ...prev, [field]: value }));
  };

  const handleZoneChange = (chamberNumber: number, zone: string) => {
    console.log(`🔄 Changing chamber ${chamberNumber} in ${selectedKiln} to zone: ${zone}`);
    updateChamberZone.mutate({
      kiln_id: selectedKiln,
      chamber_number: chamberNumber,
      zone
    }, {
      onSuccess: (data) => {
        console.log(`✅ Successfully updated chamber ${chamberNumber} to ${zone}:`, data);
        toast.success(`Chamber ${chamberNumber} set to ${zone}`);
      },
      onError: (error) => {
        console.error(`❌ Failed to update chamber ${chamberNumber}:`, error);
        toast.error(`Failed to update chamber ${chamberNumber}`);
      }
    });
  };

  const handleSubmitMeasurement = () => {
    const requiredFields = ['fire_zone_temp', 'o2', 'co2', 'draught_pressure'];
    const missingFields = requiredFields.filter(field => !measurementData[field as keyof MeasurementData]);

    if (missingFields.length > 0) {
      toast.error("Please fill in all required measurement fields");
      return;
    }

    const currentZone = chamberZones.find(
      z => z.kiln_id === selectedKiln && z.chamber_number === selectedChamber
    )?.zone || "Inactive";

    const parameters: Record<string, number> = {};
    Object.entries(measurementData).forEach(([key, value]) => {
      if (key !== 'measurement_time' && value) {
        parameters[key] = parseFloat(value);
      }
    });

    createMeasurement.mutate({
      kiln_id: selectedKiln,
      chamber_number: selectedChamber,
      measurement_date: new Date().toISOString().split('T')[0],
      measurement_time: measurementData.measurement_time,
      fire_zone: currentZone,
      parameters
    }, {
      onSuccess: () => {
        setMeasurementData({
          measurement_time: "08:00",
          preheat_zone_1_temp: "",
          preheat_zone_2_temp: "",
          pre_combustion_zone_temp: "",
          fire_zone_temp: "",
          cooling_zone_1_temp: "",
          cooling_zone_2_temp: "",
          fire_position_chamber: "",
          fire_movement_meters: "",
          o2: "",
          co2: "",
          draught_pressure: "",
          fuel_to_brick_ratio_setting: "",
          brick_moisture_setting: "",
        });
        refetchMeasurements();
      }
    });
  };

  const getParameterStatus = (paramName: string, value: number) => {
    const norm = parameterNorms.find(p => p.parameter_name.toLowerCase() === paramName.toLowerCase());
    if (!norm) return "unknown";

    if (value < norm.min_value || value > norm.max_value) {
      return "critical";
    }
    return "normal";
  };

  // Generate mock active alerts based on parameter violations
  const getActiveAlerts = () => {
    const alerts = [
      { kiln: "Habla Kiln Ch1", parameter: "O₂", expected: "3-5", actual: "2.1" },
      { kiln: "Habla Kiln Ch1", parameter: "CO₂", expected: "12-15", actual: "16.4" },
      { kiln: "Habla Kiln Ch1", parameter: "Fire Zone Temp", expected: "850-950", actual: "980" },
      { kiln: "Habla Kiln Ch1", parameter: "Draught Pressure", expected: "-8--4", actual: "-2.6" },
    ];
    return alerts;
  };

  const exportAlerts = () => {
    const alerts = getActiveAlerts();
    const csvContent = "data:text/csv;charset=utf-8,"
      + "Kiln,Parameter,Expected,Actual\n"
      + alerts.map(alert => `${alert.kiln},${alert.parameter},${alert.expected},${alert.actual}`).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "active_alerts.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportCSV = () => {
    const csvContent = "data:text/csv;charset=utf-8,"
      + "Date,Time,Kiln,Chamber,Zone,Temperature,Pressure,Humidity,Oxygen\n"
      + measurements.map(m =>
          `${m.measurement_date},${m.measurement_time},${selectedKiln},${m.chamber_number},${m.fire_zone},${m.parameters.temperature || ''},${m.parameters.pressure || ''},${m.parameters.humidity || ''},${m.parameters.oxygen || ''}`
        ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "kiln_measurements.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Overview tab rendering
  const renderOverviewTab = () => {
    return (
      <div className="space-y-6">
        {/* Active Alerts Section */}
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <div className="flex items-center justify-between">
              <span className="font-semibold">Active Alerts (4)</span>
              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                <AlertTriangle className="h-4 w-4 mr-1" />
                View All
              </Button>
            </div>
            <div className="mt-2 space-y-1 text-sm">
              {getActiveAlerts().map((alert, index) => (
                <div key={index}>
                  <strong>{alert.kiln}:</strong> {alert.parameter} = {alert.actual} (Expected: {alert.expected})
                </div>
              ))}
            </div>
          </AlertDescription>
        </Alert>

        {/* Kiln Zone Overview */}
        <KilnZoneOverview />

        {/* Kiln Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {KILN_OPTIONS.map((kiln) => {
            const kilnZones = chamberZones.filter(z => z.kiln_id === kiln.id);
            const activeAlerts = kiln.id === "habla-kiln" ? 4 : 0;
            const activeZones = kilnZones.filter(z => z.zone !== "Inactive").length;
            const measurements = 2; // Mock data

            return (
              <Card key={kiln.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{kiln.name}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-slate-600">Chambers:</span>
                    <span className="font-semibold">{kiln.chambers}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-slate-600">Measurements:</span>
                    <span className="font-semibold">{measurements}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-slate-600">Active Alerts:</span>
                    <span className={`font-semibold ${activeAlerts > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {activeAlerts}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-slate-600">Active Zones:</span>
                    <span className="font-semibold">{activeZones}</span>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    );
  };

  const renderChamberGrid = () => {
    const chambers = Array.from({ length: maxChambers }, (_, i) => i + 1);

    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-12 gap-2 sm:gap-3">
        {chambers.map((chamber) => {
          const zone = chamberZones.find(
            z => z.kiln_id === selectedKiln && z.chamber_number === chamber
          )?.zone || "Inactive";

          const isSelected = selectedChamber === chamber;
          const zoneColor = ZONE_COLORS[zone as keyof typeof ZONE_COLORS] || ZONE_COLORS.Inactive;

          return (
            <Card
              key={chamber}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? "ring-2 ring-blue-500" : ""
              }`}
              style={{ backgroundColor: zoneColor }}
              onClick={() => setSelectedChamber(chamber)}
            >
              <CardContent className="p-2 sm:p-3 text-center">
                <div className="text-xs sm:text-sm font-semibold mb-1 sm:mb-2">Ch {chamber}</div>
                <Select
                  value={zone}
                  onValueChange={(value) => handleZoneChange(chamber, value)}
                >
                  <SelectTrigger className="w-full h-6 sm:h-8 text-xs sm:text-sm bg-white/90 backdrop-blur-sm">
                    <SelectValue className="text-xs sm:text-sm" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border shadow-lg z-50">
                    {ZONE_OPTIONS.map((zoneOption) => (
                      <SelectItem key={zoneOption} value={zoneOption} className="text-xs sm:text-sm">
                        {zoneOption}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {/* Show zone name below dropdown on mobile for better visibility */}
                <div className="mt-1 text-xs text-gray-700 font-medium truncate sm:hidden">
                  {zone}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  };

  // Enhanced data entry form
  const renderDataEntryForm = () => {
    return (
      <div className="space-y-6">
        {/* Measurement Time and Kiln/Chamber Selection */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="measurement_time">Measurement Time</Label>
            <Input
              id="measurement_time"
              type="time"
              value={measurementData.measurement_time}
              onChange={(e) => handleMeasurementChange("measurement_time", e.target.value)}
            />
          </div>
          <div>
            <Label>Kiln</Label>
            <Select value={selectedKiln} onValueChange={setSelectedKiln}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {KILN_OPTIONS.map((kiln) => (
                  <SelectItem key={kiln.id} value={kiln.id}>
                    {kiln.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>Chamber</Label>
            <Select value={selectedChamber.toString()} onValueChange={(value) => setSelectedChamber(parseInt(value))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: maxChambers }, (_, i) => i + 1).map((chamber) => (
                  <SelectItem key={chamber} value={chamber.toString()}>
                    Chamber {chamber}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Parameter Input Fields - Updated with new parameters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="preheat_zone_1_temp">Pre-heat Zone 1 Temp (°C)</Label>
            <Input
              id="preheat_zone_1_temp"
              type="number"
              value={measurementData.preheat_zone_1_temp}
              onChange={(e) => handleMeasurementChange("preheat_zone_1_temp", e.target.value)}
              placeholder="200 - 350"
            />
          </div>
          <div>
            <Label htmlFor="preheat_zone_2_temp">Pre-heat Zone 2 Temp (°C)</Label>
            <Input
              id="preheat_zone_2_temp"
              type="number"
              value={measurementData.preheat_zone_2_temp}
              onChange={(e) => handleMeasurementChange("preheat_zone_2_temp", e.target.value)}
              placeholder="350 - 500"
            />
          </div>
          <div>
            <Label htmlFor="pre_combustion_zone_temp">Pre-combustion Zone Temp (°C)</Label>
            <Input
              id="pre_combustion_zone_temp"
              type="number"
              value={measurementData.pre_combustion_zone_temp}
              onChange={(e) => handleMeasurementChange("pre_combustion_zone_temp", e.target.value)}
              placeholder="500 - 700"
            />
          </div>
          <div>
            <Label htmlFor="fire_zone_temp">Fire Zone Temp (°C)</Label>
            <Input
              id="fire_zone_temp"
              type="number"
              value={measurementData.fire_zone_temp}
              onChange={(e) => handleMeasurementChange("fire_zone_temp", e.target.value)}
              placeholder="850 - 950"
            />
          </div>
          <div>
            <Label htmlFor="cooling_zone_1_temp">Cooling Zone 1 Temp (°C)</Label>
            <Input
              id="cooling_zone_1_temp"
              type="number"
              value={measurementData.cooling_zone_1_temp}
              onChange={(e) => handleMeasurementChange("cooling_zone_1_temp", e.target.value)}
              placeholder="200 - 400"
            />
          </div>
          <div>
            <Label htmlFor="cooling_zone_2_temp">Cooling Zone 2 Temp (°C)</Label>
            <Input
              id="cooling_zone_2_temp"
              type="number"
              value={measurementData.cooling_zone_2_temp}
              onChange={(e) => handleMeasurementChange("cooling_zone_2_temp", e.target.value)}
              placeholder="50 - 200"
            />
          </div>
          <div>
            <Label htmlFor="fire_position_chamber">Fire Position in Chamber (m)</Label>
            <Input
              id="fire_position_chamber"
              type="number"
              step="0.1"
              value={measurementData.fire_position_chamber}
              onChange={(e) => handleMeasurementChange("fire_position_chamber", e.target.value)}
              placeholder="0 - 25"
            />
          </div>
          <div>
            <Label htmlFor="fire_movement_meters">Fire Movement (Meters)</Label>
            <Input
              id="fire_movement_meters"
              type="number"
              step="0.1"
              value={measurementData.fire_movement_meters}
              onChange={(e) => handleMeasurementChange("fire_movement_meters", e.target.value)}
              placeholder="2.5 - 3.75"
            />
          </div>
          <div>
            <Label htmlFor="o2">O₂ (%)</Label>
            <Input
              id="o2"
              type="number"
              step="0.1"
              value={measurementData.o2}
              onChange={(e) => handleMeasurementChange("o2", e.target.value)}
              placeholder="3 - 5"
            />
          </div>
          <div>
            <Label htmlFor="co2">CO₂ (%)</Label>
            <Input
              id="co2"
              type="number"
              step="0.1"
              value={measurementData.co2}
              onChange={(e) => handleMeasurementChange("co2", e.target.value)}
              placeholder="12 - 15"
            />
          </div>
          <div>
            <Label htmlFor="draught_pressure">Draught Pressure (mmWC)</Label>
            <Input
              id="draught_pressure"
              type="number"
              step="0.1"
              value={measurementData.draught_pressure}
              onChange={(e) => handleMeasurementChange("draught_pressure", e.target.value)}
              placeholder="-8 - -4"
            />
          </div>
          <div>
            <Label htmlFor="fuel_to_brick_ratio_setting">Fuel to Brick Ratio - Setting (%)</Label>
            <Input
              id="fuel_to_brick_ratio_setting"
              type="number"
              step="0.1"
              value={measurementData.fuel_to_brick_ratio_setting}
              onChange={(e) => handleMeasurementChange("fuel_to_brick_ratio_setting", e.target.value)}
              placeholder="5 - 6.5"
            />
          </div>
          <div>
            <Label htmlFor="brick_moisture_setting">Brick Moisture % - Setting (%)</Label>
            <Input
              id="brick_moisture_setting"
              type="number"
              step="0.1"
              value={measurementData.brick_moisture_setting}
              onChange={(e) => handleMeasurementChange("brick_moisture_setting", e.target.value)}
              placeholder="7.9 - 10"
            />
          </div>
        </div>

        <Button
          onClick={handleSubmitMeasurement}
          disabled={createMeasurement.isPending}
          className="bg-slate-800 hover:bg-slate-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Record Measurement
        </Button>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header with Export Buttons */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl sm:text-2xl font-bold text-slate-800">Kiln Monitoring Dashboard</h2>
          <p className="text-sm sm:text-base text-slate-600">Real-time monitoring and control of zig-zag kilns</p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <Button variant="outline" onClick={exportAlerts} className="flex items-center gap-2 text-xs sm:text-sm">
            <Download className="h-3 w-3 sm:h-4 sm:w-4" />
            Export Alerts
          </Button>
          <Button variant="outline" onClick={exportCSV} className="flex items-center gap-2 text-xs sm:text-sm">
            <FileText className="h-3 w-3 sm:h-4 sm:w-4" />
            Export CSV
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3 sm:grid-cols-6 text-xs sm:text-sm">
          <TabsTrigger value="overview" className="text-xs sm:text-sm">Overview</TabsTrigger>
          <TabsTrigger value="data-entry" className="text-xs sm:text-sm">Data Entry</TabsTrigger>
          <TabsTrigger value="zone-control" className="text-xs sm:text-sm">Zone Control</TabsTrigger>
          <TabsTrigger value="parameter-norms" className="text-xs sm:text-sm">Parameter Norms</TabsTrigger>
          <TabsTrigger value="daily-summary" className="text-xs sm:text-sm">Daily Summary</TabsTrigger>
          <TabsTrigger value="trends" className="text-xs sm:text-sm">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {renderOverviewTab()}
        </TabsContent>

        <TabsContent value="data-entry" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-4 w-4 sm:h-5 sm:w-5" />
                Data Entry
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderDataEntryForm()}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="zone-control" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Zone Control</CardTitle>
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
                <p className="text-sm text-slate-600">
                  {currentKiln?.name} - Zone Assignment
                </p>
                <Select value={selectedKiln} onValueChange={setSelectedKiln}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-white border shadow-lg z-50">
                    {KILN_OPTIONS.map((kiln) => (
                      <SelectItem key={kiln.id} value={kiln.id}>
                        {kiln.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              {renderChamberGrid()}
            </CardContent>
          </Card>

          {/* Zone Color Legend */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Zone Color Legend</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:flex lg:flex-wrap gap-2 sm:gap-4">
                {Object.entries(ZONE_COLORS).map(([zone, color]) => (
                  <div key={zone} className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 sm:w-4 sm:h-4 rounded border"
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-xs sm:text-sm">{zone}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="parameter-norms" className="space-y-4">
          <EnhancedParameterNorms />
        </TabsContent>

        <TabsContent value="daily-summary" className="space-y-4">
          <SimplifiedDailySummary />
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <ParameterTrends />
        </TabsContent>
      </Tabs>
    </div>
  );
};
