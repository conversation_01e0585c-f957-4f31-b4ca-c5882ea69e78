
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import {
  getParameterNorms,
  updateParameterNorm,
  getEnhancedParameterNorms,
  updateEnhancedParameterNorm,
  getMeasurements,
  createMeasurement,
  getChamberZones,
  updateChamberZone,
  initializeChamberZones,
  createMeasurementAction,
  updateMeasurementAction,
  getMeasurementActions,
  getDailySummary,
  KilnParameterNorm,
  KilnMonitoringMeasurement,
  KilnChamberZone
} from "@/data/kilnMonitoringData";
import {
  EnhancedKilnParameterNorm,
  KilnMeasurementAction,
  DailySummary
} from "@/types/kilnMonitoring";

export const useParameterNorms = () => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ['kiln-parameter-norms'],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getParameterNorms(userId || undefined);
    },
  });
};

export const useUpdateParameterNorm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<KilnParameterNorm> }) =>
      updateParameterNorm(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kiln-parameter-norms'] });
      queryClient.invalidateQueries({ queryKey: ['enhanced-kiln-parameter-norms'] });
      toast.success('Parameter norm updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update parameter norm: ${error.message}`);
    },
  });
};

// Enhanced parameter norms hooks
export const useEnhancedParameterNorms = () => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ['enhanced-kiln-parameter-norms'],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getEnhancedParameterNorms(userId || undefined);
    },
  });
};

export const useUpdateEnhancedParameterNorm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<EnhancedKilnParameterNorm> }) =>
      updateEnhancedParameterNorm(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['enhanced-kiln-parameter-norms'] });
      queryClient.invalidateQueries({ queryKey: ['kiln-parameter-norms'] });
      toast.success('Parameter norm updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update parameter norm: ${error.message}`);
    },
  });
};

export const useMeasurements = (filters?: {
  kiln_id?: string;
  date?: string;
  parameter?: string;
}) => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ['kiln-measurements', filters],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getMeasurements(filters, userId || undefined);
    },
  });
};

export const useCreateMeasurement = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (measurement: Omit<KilnMonitoringMeasurement, 'id' | 'created_at' | 'updated_at'>) =>
      createMeasurement(measurement),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kiln-measurements'] });
      toast.success('Measurement recorded successfully');
    },
    onError: (error) => {
      toast.error(`Failed to record measurement: ${error.message}`);
    },
  });
};

export const useChamberZones = () => {
  return useQuery({
    queryKey: ['kiln-chamber-zones'],
    queryFn: getChamberZones,
    retry: 1, // Reduce retry attempts to prevent excessive loading
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateChamberZone = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ kiln_id, chamber_number, zone }: {
      kiln_id: string;
      chamber_number: number;
      zone: string;
    }) => updateChamberZone(kiln_id, chamber_number, zone),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kiln-chamber-zones'] });
    },
    onError: (error) => {
      console.error('Failed to update chamber zone:', error);
      // Don't show toast for zone updates to avoid spam
    },
  });
};

export const useInitializeChamberZones = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ kiln_id, chamber_count }: {
      kiln_id: string;
      chamber_count: number;
    }) => initializeChamberZones(kiln_id, chamber_count),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kiln-chamber-zones'] });
    },
    onError: (error) => {
      console.error('Failed to initialize chamber zones:', error);
      // Don't show toast for initialization errors to avoid breaking the UI
    },
  });
};

// Measurement actions hooks
export const useMeasurementActions = (measurementId: string) => {
  return useQuery({
    queryKey: ['kiln-measurement-actions', measurementId],
    queryFn: () => getMeasurementActions(measurementId),
    enabled: !!measurementId,
  });
};

export const useCreateMeasurementAction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (action: Omit<KilnMeasurementAction, 'id' | 'created_at' | 'updated_at'>) =>
      createMeasurementAction(action),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['kiln-measurement-actions'] });
      queryClient.invalidateQueries({ queryKey: ['daily-summary'] });
      toast.success('Measurement action recorded successfully');
    },
    onError: (error) => {
      toast.error(`Failed to record measurement action: ${error.message}`);
    },
  });
};

export const useUpdateMeasurementAction = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<KilnMeasurementAction> }) =>
      updateMeasurementAction(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['kiln-measurement-actions'] });
      queryClient.invalidateQueries({ queryKey: ['daily-summary'] });
      toast.success('Measurement action updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update measurement action: ${error.message}`);
    },
  });
};

// Daily summary hook
export const useDailySummary = (date: string) => {
  return useQuery({
    queryKey: ['daily-summary', date],
    queryFn: () => getDailySummary(date),
    enabled: !!date,
  });
};
