
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Settings, Users, Bell, Shield, Database, Palette, Globe } from "lucide-react";
import { useSystemConfig } from "@/hooks/useSystemConfig";
import { useNotificationSettings } from "@/hooks/useNotificationSettings";
import { supabase } from "@/integrations/supabase/client";
import { UserManagementSection } from "@/components/settings/UserManagementSection";
import { useAuth } from "@/contexts/AuthContext";

export const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState("general");
  const { currentUser } = useAuth();
  const { data: systemConfig, isLoading: isConfigLoading, error: configError } = useSystemConfig();
  const { data: notificationSettings, isLoading: isNotificationLoading, error: notificationError } = useNotificationSettings(currentUser?.id || '');

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <div className="container py-10">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
        <p className="text-muted-foreground">
          Manage your app settings and preferences.
        </p>
      </div>
      <Separator className="my-4" />
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="mb-4">
          <TabsTrigger value="general">
            <Settings className="mr-2 h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="mr-2 h-4 w-4" />
            Users
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="mr-2 h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="security">
            <Shield className="mr-2 h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="database">
            <Database className="mr-2 h-4 w-4" />
            Database
          </TabsTrigger>
          <TabsTrigger value="appearance">
            <Palette className="mr-2 h-4 w-4" />
            Appearance
          </TabsTrigger>
          <TabsTrigger value="localization">
            <Globe className="mr-2 h-4 w-4" />
            Localization
          </TabsTrigger>
        </TabsList>
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Configuration</CardTitle>
              <CardDescription>
                Configure general system settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isConfigLoading ? (
                <div>Loading system configuration...</div>
              ) : configError ? (
                <div>Error: {configError.message}</div>
              ) : (
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">Company Name</Label>
                    <Input id="companyName" defaultValue={systemConfig?.company_name} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="defaultTheme">Default Theme</Label>
                    <Select>
                      <SelectTrigger id="defaultTheme">
                        <SelectValue placeholder="Select theme" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="users" className="space-y-4">
            <UserManagementSection />
        </TabsContent>
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Manage your notification settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isNotificationLoading ? (
                <div>Loading notification settings...</div>
              ) : notificationError ? (
                <div>Error: {notificationError.message}</div>
              ) : (
                <div className="grid gap-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="emailNotifications">Email Notifications</Label>
                    <Switch
                      id="emailNotifications"
                      defaultChecked={notificationSettings?.email_notifications}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="productionAlerts">Production Alerts</Label>
                    <Switch
                      id="productionAlerts"
                      defaultChecked={notificationSettings?.production_alerts}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="fuelLevelWarnings">Fuel Level Warnings</Label>
                    <Switch
                      id="fuelLevelWarnings"
                      defaultChecked={notificationSettings?.fuel_level_warnings}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="employeeUpdates">Employee Updates</Label>
                    <Switch
                      id="employeeUpdates"
                      defaultChecked={notificationSettings?.employee_updates}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Configure your security settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input id="password" type="password" placeholder="••••••••" />
                </div>
                <Button>Change Password</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="database" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Database Management</CardTitle>
              <CardDescription>
                Manage and configure your database settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label>Database Version</Label>
                  <Badge variant="secondary">v1.0.0</Badge>
                </div>
                <Button disabled>Backup Database</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="appearance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Appearance</CardTitle>
              <CardDescription>
                Customize the look and feel of your application.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="theme">Theme</Label>
                  <Select>
                    <SelectTrigger id="theme">
                      <SelectValue placeholder="Select theme" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="system">System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="localization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Localization</CardTitle>
              <CardDescription>
                Configure language and regional settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <Select>
                    <SelectTrigger id="language">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
