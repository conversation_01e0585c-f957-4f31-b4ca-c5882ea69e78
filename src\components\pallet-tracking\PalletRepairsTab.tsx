
import { useState } from "react";
import { <PERSON>, Card<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Wrench, Package, AlertTriangle, Trash2, Edit } from "lucide-react";
import { toast } from "sonner";
import { usePalletRepairs, useCreatePalletRepair, useUpdatePalletRepair, useDeletePalletRepair } from "@/hooks/usePalletRepairs";

export const PalletRepairsTab = () => {
  // Use hooks for data management
  const { data: repairEntries = [], isLoading } = usePalletRepairs();
  const createMutation = useCreatePalletRepair();
  const updateMutation = useUpdatePalletRepair();
  const deleteMutation = useDeletePalletRepair();

  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    pallets_repaired: "",
    broken_received: "",
    broken_waiting: "",
    factory_pallet_count: "",
    strapping_pallet_count: "",
    repairer: "",
    notes: ""
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.id]: e.target.value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.date || !formData.pallets_repaired || !formData.repairer) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (editingId) {
      // Update existing entry
      updateMutation.mutate({
        id: editingId,
        date: formData.date,
        pallets_repaired: parseInt(formData.pallets_repaired),
        broken_received: parseInt(formData.broken_received) || 0,
        broken_waiting: parseInt(formData.broken_waiting) || 0,
        factory_pallet_count: formData.factory_pallet_count ? parseInt(formData.factory_pallet_count) : undefined,
        strapping_pallet_count: formData.strapping_pallet_count ? parseInt(formData.strapping_pallet_count) : undefined,
        repairer: formData.repairer,
        notes: formData.notes || undefined,
      }, {
        onSuccess: () => {
          setEditingId(null);
          resetForm();
        },
      });
    } else {
      // Create new entry
      createMutation.mutate({
        date: formData.date,
        pallets_repaired: parseInt(formData.pallets_repaired),
        broken_received: parseInt(formData.broken_received) || 0,
        broken_waiting: parseInt(formData.broken_waiting) || 0,
        factory_pallet_count: formData.factory_pallet_count ? parseInt(formData.factory_pallet_count) : undefined,
        strapping_pallet_count: formData.strapping_pallet_count ? parseInt(formData.strapping_pallet_count) : undefined,
        repairer: formData.repairer,
        notes: formData.notes || undefined,
      }, {
        onSuccess: () => {
          resetForm();
        },
      });
    }
  };

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split('T')[0],
      pallets_repaired: "",
      broken_received: "",
      broken_waiting: "",
      factory_pallet_count: "",
      strapping_pallet_count: "",
      repairer: "",
      notes: ""
    });
  };

  const handleEdit = (entry: any) => {
    setEditingId(entry.id);
    setFormData({
      date: entry.date,
      pallets_repaired: entry.pallets_repaired.toString(),
      broken_received: entry.broken_received.toString(),
      broken_waiting: entry.broken_waiting.toString(),
      factory_pallet_count: entry.factory_pallet_count?.toString() || "",
      strapping_pallet_count: entry.strapping_pallet_count?.toString() || "",
      repairer: entry.repairer,
      notes: entry.notes || ""
    });
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    resetForm();
  };

  const handleDelete = (id: string) => {
    if (confirm("Are you sure you want to delete this repair entry?")) {
      deleteMutation.mutate(id);
    }
  };

  const totalRepaired = repairEntries.reduce((sum, entry) => sum + entry.pallets_repaired, 0);
  const totalBrokenReceived = repairEntries.reduce((sum, entry) => sum + entry.broken_received, 0);
  const totalBrokenWaiting = repairEntries.reduce((sum, entry) => sum + entry.broken_waiting, 0);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-green-600">{totalRepaired}</div>
                <p className="text-slate-600">Total Repaired</p>
              </div>
              <Wrench className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-blue-600">{totalBrokenReceived}</div>
                <p className="text-slate-600">Broken Received</p>
              </div>
              <Package className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-red-600">{totalBrokenWaiting}</div>
                <p className="text-slate-600">Waiting Repair</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recording Form */}
      <Card>
        <CardHeader>
          <CardTitle>Record Pallet Repairs</CardTitle>
          <p className="text-sm text-slate-600">Track daily pallet repair activities and counts</p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="date">Date *</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="repairer">Repairer Name *</Label>
                <Input
                  id="repairer"
                  type="text"
                  value={formData.repairer}
                  onChange={handleInputChange}
                  placeholder="Enter repairer name"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="pallets_repaired">Pallets Repaired *</Label>
                <Input
                  id="pallets_repaired"
                  type="number"
                  value={formData.pallets_repaired}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                  required
                />
              </div>

              <div>
                <Label htmlFor="broken_received">Broken Pallets Received</Label>
                <Input
                  id="broken_received"
                  type="number"
                  value={formData.broken_received}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                />
              </div>

              <div>
                <Label htmlFor="broken_waiting">Broken Pallets Waiting Repair</Label>
                <Input
                  id="broken_waiting"
                  type="number"
                  value={formData.broken_waiting}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                />
              </div>

              <div>
                <Label htmlFor="factory_pallet_count">Factory Pallet Count</Label>
                <Input
                  id="factory_pallet_count"
                  type="number"
                  value={formData.factory_pallet_count}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                />
                <p className="text-xs text-slate-500 mt-1">Fortnightly pallet count</p>
              </div>

              <div>
                <Label htmlFor="strapping_pallet_count">Strapping Pallet Count</Label>
                <Input
                  id="strapping_pallet_count"
                  type="number"
                  value={formData.strapping_pallet_count}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                />
                <p className="text-xs text-slate-500 mt-1">Strapping area count</p>
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Input
                  id="notes"
                  type="text"
                  value={formData.notes}
                  onChange={handleInputChange}
                  placeholder="Additional notes..."
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                type="submit"
                className="bg-slate-800 hover:bg-slate-700"
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                {editingId ? "Update Entry" : "Record Repair Entry"}
              </Button>
              {editingId && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelEdit}
                >
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Repair Entries Table */}
      <Card>
        <CardHeader>
          <CardTitle>Repair History</CardTitle>
          <p className="text-sm text-slate-600">Recent pallet repair entries</p>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8 text-slate-500">
              Loading repair entries...
            </div>
          ) : repairEntries.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              No repair entries recorded yet
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Repairer</TableHead>
                  <TableHead>Pallets Repaired</TableHead>
                  <TableHead>Broken Received</TableHead>
                  <TableHead>Waiting Repair</TableHead>
                  <TableHead>Factory Count</TableHead>
                  <TableHead>Strapping Count</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {repairEntries.map((entry) => (
                  <TableRow key={entry.id}>
                    <TableCell>{entry.date}</TableCell>
                    <TableCell>{entry.repairer}</TableCell>
                    <TableCell>{entry.pallets_repaired}</TableCell>
                    <TableCell>{entry.broken_received}</TableCell>
                    <TableCell>{entry.broken_waiting}</TableCell>
                    <TableCell>{entry.factory_pallet_count || '-'}</TableCell>
                    <TableCell>{entry.strapping_pallet_count || '-'}</TableCell>
                    <TableCell>{entry.notes || '-'}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(entry)}
                          disabled={editingId === entry.id}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(entry.id)}
                          disabled={deleteMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
