
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const usePerformanceAnalytics = () => {
  return useQuery({
    queryKey: ['performance-analytics'],
    queryFn: async () => {
      console.log('Calling AI performance analytics');
      
      const { data, error } = await supabase.functions.invoke('ai-business-intelligence', {
        body: { 
          type: 'performance',
          params: {}
        }
      });

      if (error) {
        console.error('Error calling AI performance analytics:', error);
        throw error;
      }

      return data?.data || {
        teams: [],
        individuals: [],
        departments: [],
        recommendations: []
      };
    },
    refetchInterval: 300000, // Refetch every 5 minutes
  });
};
