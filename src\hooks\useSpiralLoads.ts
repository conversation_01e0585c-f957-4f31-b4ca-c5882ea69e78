
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { toast } from "sonner";

export interface SpiralLoad {
  id: number;
  created_at: string;
  date: string;
  dnote_no: string;
  supplier: string;
  mine: string;
  wet_weight: number;
  dry_weight: number;
  carbon: number;
  user_id: string;
}

export interface NewSpiralLoad {
  date: string;
  dnote_no: string;
  supplier: string;
  mine: string;
  wet_weight: number;
  dry_weight: number;
  carbon: number;
}

export function useSpiralLoads() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["spiralLoads"],
    queryFn: async () => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { data, error } = await supabase
        .from("spiral_loads")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching spiral loads:", error);
        throw new Error(error.message);
      }

      return (data || []) as SpiralLoad[];
    },
  });
}

export function useAddSpiralLoad() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (newLoad: NewSpiralLoad) => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      await setUserContext(userId);

      const { data, error } = await supabase
        .from("spiral_loads")
        .insert({
          date: newLoad.date,
          dnote_no: newLoad.dnote_no,
          supplier: newLoad.supplier,
          mine: newLoad.mine,
          wet_weight: newLoad.wet_weight,
          dry_weight: newLoad.dry_weight,
          carbon: newLoad.carbon,
          user_id: userId,
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding spiral load:", error);
        throw new Error(error.message);
      }

      return data as SpiralLoad;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["spiralLoads"] });
    },
  });
}
