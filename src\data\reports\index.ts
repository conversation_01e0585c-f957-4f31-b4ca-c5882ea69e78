import { TimeRange, CustomDateRange } from "@/components/dashboard/DashboardContent";
import { ReportType } from "@/components/pages/ReportsPage";
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, format } from 'date-fns';

import { getEmployeeHoursReport } from './employeeHours';
import { getFactoryOutputReport } from './factoryOutput';
import { getSettingTeamsReport } from './settingTeams';
import { getDehackingReport } from './dehacking';
import { getFuelManagementReport } from './fuelManagement';
import { getUserActivityReport } from './userActivity';
import type { ReportData } from "./types";
export type { ReportData };

// --- Date Range Helper ---
const getDateRange = (timeRange: TimeRange, customDateRange?: CustomDateRange) => {
  const now = new Date();
  switch (timeRange) {
    case 'today':
      return { from: format(startOfDay(now), 'yyyy-MM-dd'), to: format(endOfDay(now), 'yyyy-MM-dd') };
    case 'week':
      return { from: format(startOfWeek(now, { weekStartsOn: 1 }), 'yyyy-MM-dd'), to: format(endOfWeek(now, { weekStartsOn: 1 }), 'yyyy-MM-dd') };
    case 'month':
      return { from: format(startOfMonth(now), 'yyyy-MM-dd'), to: format(endOfMonth(now), 'yyyy-MM-dd') };
    case 'year':
      return { from: format(startOfYear(now), 'yyyy-MM-dd'), to: format(endOfYear(now), 'yyyy-MM-dd') };
    case 'custom':
      return customDateRange ? { from: customDateRange.from, to: customDateRange.to } : { from: format(startOfMonth(now), 'yyyy-MM-dd'), to: format(endOfMonth(now), 'yyyy-MM-dd') };
  }
};


// --- Main Exported Function ---
export const getReportData = async ({ timeRange, reportType, customDateRange, userId }: { timeRange: TimeRange, reportType: ReportType, customDateRange?: CustomDateRange, userId?: string }): Promise<ReportData> => {
  const { from, to } = getDateRange(timeRange, customDateRange);

  switch (reportType) {
    case 'employee-hours':
      return getEmployeeHoursReport({ from, to }, userId);
    case 'factory-output':
      return getFactoryOutputReport({ from, to }, userId);
    case 'setting-teams':
      return getSettingTeamsReport({ from, to }, userId);
    case 'dehacking':
      return getDehackingReport({ from, to, userId });
    case 'fuel-management':
      return getFuelManagementReport({ from, to }, userId);
    case 'user-activity':
      return getUserActivityReport({ from, to }, userId);
    default:
      return { main: [], secondary: [] };
  }
};
