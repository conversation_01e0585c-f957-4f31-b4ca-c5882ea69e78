
import React from "react";
import { useQuery } from "@tanstack/react-query";
import { getForkliftAllocations, ForkliftAllocation } from "@/data/forkliftAllocations";
import { getEmployees, Employee } from "@/data/employeeData";
import { useQuery as useRQ } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { computeForkliftPerformances } from "@/utils/forkliftPerformance";
import { Loader2, Truck } from "lucide-react";

// Teams sample loader (should be replaced with real teams if available)
const TEAMS_SAMPLE = [
  { id: "setting_team_1", name: "Setting Team 1" },
  { id: "setting_team_2", name: "Setting Team 2" },
  { id: "dehacking_team_1", name: "Dehacking Team 1" },
];

export const ForkliftPerformanceDashboard = () => {
  const { data: allocations = [], isLoading: isLoadingA } = useRQ<ForkliftAllocation[]>({
    queryKey: ["forkliftAllocations"],
    queryFn: getForkliftAllocations,
  });
  const { data: employees = [], isLoading: isLoadingE } = useQuery<Employee[]>({
    queryKey: ["employees"], 
    queryFn: () => getEmployees(),
  });
  // You should replace with a dynamic team fetch for real-world use
  const teams = TEAMS_SAMPLE;

  const results = computeForkliftPerformances({
    allocations,
    employees,
    teams,
  });

  if (isLoadingA || isLoadingE) {
    return <div className="flex gap-2 items-center"><Loader2 className="animate-spin" /> Loading forklift driver data...</div>;
  }

  return (
    <Card className="mb-4 border-blue-300">
      <CardHeader>
        <CardTitle className="flex gap-2 items-center text-blue-800">
          <Truck className="text-blue-700" /> Forklift Driver Performance
        </CardTitle>
        <span className="text-blue-600 text-xs">Shows live allocated teams and production</span>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-blue-50">
                <th className="py-2 px-4 text-left">Driver</th>
                <th className="py-2 px-4 text-left">Role</th>
                <th className="py-2 px-4 text-left">Allocated</th>
                <th className="py-2 px-4 text-left">Total Pallets</th>
              </tr>
            </thead>
            <tbody>
              {results.length === 0 && (
                <tr>
                  <td colSpan={4} className="text-center py-8 text-slate-500">No forklift allocations found.</td>
                </tr>
              )}
              {results.map((r, idx) => (
                <tr key={idx} className="border-b hover:bg-blue-50">
                  <td className="py-2 px-4 font-semibold">{r.driverName}</td>
                  <td className="py-2 px-4 capitalize">{r.allocationType} forklift</td>
                  <td className="py-2 px-4">{r.allocatedNames.join(", ")}</td>
                  <td className="py-2 px-4 font-mono text-blue-700">{r.totalPallets}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};
