
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getEmployees, addEmployee, updateEmployee, deleteEmployee, Employee, EmployeeFormValues } from "@/data/employeeData";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { useToast } from "@/hooks/use-toast";

export function useEmployees() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery<Employee[], Error>({
    queryKey: ['employees'],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getEmployees(userId || undefined);
    },
  });
}

export function useAddEmployee() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (employee: EmployeeFormValues) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return addEmployee(employee, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({ title: "Success", description: "Employee added successfully." });
    },
    onError: (error) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });
}

export function useUpdateEmployee() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (employee: Partial<EmployeeFormValues> & { id: number }) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return updateEmployee(employee, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({ title: "Success", description: "Employee updated successfully." });
    },
    onError: (error) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });
}

export function useDeleteEmployee() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: number) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return deleteEmployee(id, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({ title: "Success", description: "Employee deleted successfully." });
    },
    onError: (error) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });
}
