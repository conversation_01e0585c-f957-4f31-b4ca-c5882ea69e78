import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Tit<PERSON> } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

const rescheduleSchema = z.object({
  newDate: z.date({
    required_error: "Please select a new date",
  }),
});

type RescheduleFormData = z.infer<typeof rescheduleSchema>;

interface Load {
  id: string;
  date: string;
  client_name: string;
  load_description?: string;
  transporter: string;
  load_type: "Strapped" | "Pallets";
  brick_count: number;
  rank?: number;
  brick_type_id?: string;
}

interface RescheduleLoadDialogProps {
  load: Load;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const RescheduleLoadDialog = ({ load, open, onOpenChange }: RescheduleLoadDialogProps) => {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  const {
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<RescheduleFormData>({
    resolver: zodResolver(rescheduleSchema),
    defaultValues: {
      newDate: new Date(load.date),
    }
  });

  const watchedDate = watch("newDate");

  const onSubmit = async (data: RescheduleFormData) => {
    try {
      // Set user context before database operation
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { error } = await supabase
        .from('load_planning')
        .update({
          date: format(data.newDate, 'yyyy-MM-dd'),
          updated_at: new Date().toISOString()
        })
        .eq('id', load.id);

      if (error) {
        console.error("Database error:", error);
        throw error;
      }

      toast.success(`Load for ${load.client_name} rescheduled to ${format(data.newDate, 'PPP')}!`);
      queryClient.invalidateQueries({ queryKey: ['load-planning'] });
      onOpenChange(false);
    } catch (error) {
      console.error("Error rescheduling load:", error);
      toast.error("Failed to reschedule load");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Reschedule Load</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Change the delivery date for <strong>{load.client_name}</strong>
          </p>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            <div className="text-sm text-slate-600 bg-slate-50 p-3 rounded">
              <p><strong>Current Date:</strong> {format(new Date(load.date), 'PPP')}</p>
              <p><strong>Client:</strong> {load.client_name}</p>
              <p><strong>Transporter:</strong> {load.transporter}</p>
              <p><strong>Quantity:</strong> {load.brick_count.toLocaleString()} bricks ({load.load_type})</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="newDate">New Delivery Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !watchedDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {watchedDate ? format(watchedDate, "PPP") : "Pick a new date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={watchedDate}
                    onSelect={(date) => date && setValue("newDate", date)}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {errors.newDate && <p className="text-sm text-red-500">{errors.newDate.message}</p>}
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Rescheduling..." : "Reschedule Load"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
