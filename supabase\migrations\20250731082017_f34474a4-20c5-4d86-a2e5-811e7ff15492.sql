
-- Phase 1: Critical Security Fixes - <PERSON><PERSON> Policies and Database Security

-- 1. Fix overly permissive policies on sensitive tables
DROP POLICY IF EXISTS "Allow public access to employees" ON public.employees;
DROP POLICY IF EXISTS "Allow public read access to teams" ON public.teams;
DROP POLICY IF EXISTS "Allow authenticated users full access on teams" ON public.teams;
DROP POLICY IF EXISTS "Allow public modification of team memberships" ON public.team_memberships;
DROP POLICY IF EXISTS "Allow public modification of payments" ON public.payments;

-- 2. Create secure, user-based policies for employees table
CREATE POLICY "Authenticated users can view employees" ON public.employees
    FOR SELECT USING (true);

CREATE POLICY "Managers and admins can manage employees" ON public.employees
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id::text IN (
                SELECT current_setting('app.current_user_id', true)
            ) 
            AND u.role IN ('admin', 'manager')
        )
    );

-- 3. Create secure policies for teams table
CREATE POLICY "Authenticated users can view teams" ON public.teams
    FOR SELECT USING (true);

CREATE POLICY "Ad<PERSON> can manage teams" ON public.teams
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id::text IN (
                SELECT current_setting('app.current_user_id', true)
            ) 
            AND u.role = 'admin'
        )
    );

CREATE POLICY "Admins can update teams" ON public.teams
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id::text IN (
                SELECT current_setting('app.current_user_id', true)
            ) 
            AND u.role = 'admin'
        )
    );

-- 4. Secure team memberships
CREATE POLICY "Authenticated users can view team memberships" ON public.team_memberships
    FOR SELECT USING (true);

CREATE POLICY "Managers can manage team memberships" ON public.team_memberships
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id::text IN (
                SELECT current_setting('app.current_user_id', true)
            ) 
            AND u.role IN ('admin', 'manager')
        )
    );

CREATE POLICY "Managers can update team memberships" ON public.team_memberships
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id::text IN (
                SELECT current_setting('app.current_user_id', true)
            ) 
            AND u.role IN ('admin', 'manager')
        )
    );

-- 5. Secure payments table
CREATE POLICY "Finance and managers can view payments" ON public.payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id::text IN (
                SELECT current_setting('app.current_user_id', true)
            ) 
            AND u.role IN ('admin', 'manager', 'finance')
        )
    );

CREATE POLICY "Finance can manage payments" ON public.payments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id::text IN (
                SELECT current_setting('app.current_user_id', true)
            ) 
            AND u.role IN ('admin', 'finance')
        )
    );

-- 6. Fix database functions with security definer and proper search path
CREATE OR REPLACE FUNCTION public.update_chamber_fire_status(p_kiln_id text, p_chamber_number integer, p_fire_id text, p_is_burning boolean, p_user_id uuid DEFAULT NULL::uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  INSERT INTO public.chamber_fire_status (kiln_id, chamber_number, fire_id, is_burning, updated_by, updated_at)
  VALUES (p_kiln_id, p_chamber_number, p_fire_id, p_is_burning, p_user_id, NOW())
  ON CONFLICT (kiln_id, chamber_number, fire_id)
  DO UPDATE SET 
    is_burning = EXCLUDED.is_burning,
    updated_by = EXCLUDED.updated_by,
    updated_at = NOW();
END;
$$;

CREATE OR REPLACE FUNCTION public.handle_fuel_dispensing()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  UPDATE public.fuel_bunkers
  SET current_level = current_level - NEW.quantity_liters
  WHERE id = NEW.fuel_bunker_id;
  
  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.handle_fuel_delivery()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  UPDATE public.fuel_bunkers
  SET current_level = current_level + NEW.quantity
  WHERE id = NEW.fuel_bunker_id;
  
  RETURN NEW;
END;
$$;

-- 7. Create secure user context function
CREATE OR REPLACE FUNCTION public.get_current_user_context()
RETURNS TABLE(user_id uuid, user_role user_role)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  context_user_id text;
  user_record RECORD;
BEGIN
  -- Get user ID from session context
  context_user_id := current_setting('app.current_user_id', true);
  
  IF context_user_id IS NULL OR context_user_id = '' THEN
    RETURN;
  END IF;
  
  -- Get user details
  SELECT u.id, u.role INTO user_record
  FROM public.users u 
  WHERE u.id = context_user_id::uuid AND u.active = true;
  
  IF FOUND THEN
    user_id := user_record.id;
    user_role := user_record.role;
    RETURN NEXT;
  END IF;
END;
$$;

-- 8. Create audit log table for security monitoring
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid,
    action text NOT NULL,
    table_name text,
    record_id text,
    old_values jsonb,
    new_values jsonb,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Enable RLS on audit logs
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY "Admins can view audit logs" ON public.audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id::text IN (
                SELECT current_setting('app.current_user_id', true)
            ) 
            AND u.role = 'admin'
        )
    );

-- System can insert audit logs
CREATE POLICY "System can insert audit logs" ON public.audit_logs
    FOR INSERT WITH CHECK (true);

-- 9. Create session management table for secure auth
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    session_token text NOT NULL UNIQUE,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    last_accessed timestamp with time zone NOT NULL DEFAULT now(),
    ip_address inet,
    user_agent text
);

-- Enable RLS on sessions
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- Users can only access their own sessions
CREATE POLICY "Users can view their own sessions" ON public.user_sessions
    FOR SELECT USING (user_id::text = current_setting('app.current_user_id', true));

CREATE POLICY "System can manage sessions" ON public.user_sessions
    FOR ALL WITH CHECK (true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_expires ON public.user_sessions(user_id, expires_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_created ON public.audit_logs(user_id, created_at);
