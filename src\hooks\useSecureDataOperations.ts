
import { useAuth } from '@/contexts/AuthContext';
import { logDataChange } from '@/utils/auditLogger';
import { sanitizeFormData } from '@/utils/inputValidation';

// Hook to provide secure data operations with audit logging
export const useSecureDataOperations = () => {
  const { currentUser } = useAuth();

  const secureInsert = async <T>(
    insertFn: (data: any) => Promise<T>,
    data: any,
    tableName: string,
    recordId?: string
  ): Promise<T> => {
    try {
      // Sanitize input data
      const sanitizedData = sanitizeFormData(data);
      
      // Perform the insert
      const result = await insertFn(sanitizedData);
      
      // Log the operation
      await logDataChange(
        'create',
        tableName,
        recordId || 'unknown',
        undefined,
        sanitizedData,
        currentUser?.id
      );
      
      return result;
    } catch (error) {
      console.error(`Secure insert failed for ${tableName}:`, error);
      throw error;
    }
  };

  const secureUpdate = async <T>(
    updateFn: (data: any) => Promise<T>,
    data: any,
    tableName: string,
    recordId: string,
    oldValues?: any
  ): Promise<T> => {
    try {
      // Sanitize input data
      const sanitizedData = sanitizeFormData(data);
      
      // Perform the update
      const result = await updateFn(sanitizedData);
      
      // Log the operation
      await logDataChange(
        'update',
        tableName,
        recordId,
        oldValues,
        sanitizedData,
        currentUser?.id
      );
      
      return result;
    } catch (error) {
      console.error(`Secure update failed for ${tableName}:`, error);
      throw error;
    }
  };

  const secureDelete = async <T>(
    deleteFn: () => Promise<T>,
    tableName: string,
    recordId: string,
    oldValues?: any
  ): Promise<T> => {
    try {
      // Perform the delete
      const result = await deleteFn();
      
      // Log the operation
      await logDataChange(
        'delete',
        tableName,
        recordId,
        oldValues,
        undefined,
        currentUser?.id
      );
      
      return result;
    } catch (error) {
      console.error(`Secure delete failed for ${tableName}:`, error);
      throw error;
    }
  };

  return {
    secureInsert,
    secureUpdate,
    secureDelete
  };
};
