
import bcrypt from 'bcryptjs';

export interface PasswordVerificationResult {
  isValid: boolean;
  hashType: 'bcrypt' | 'sha256' | 'plain' | 'unknown';
}

// Browser-compatible SHA256 hashing function
const sha256Hash = async (text: string): Promise<string> => {
  const encoder = new TextEncoder();
  const data = encoder.encode(text);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
};

export const verifyPassword = async (plainPassword: string, storedHash: string): Promise<PasswordVerificationResult> => {
  if (!plainPassword || !storedHash) {
    return { isValid: false, hashType: 'unknown' };
  }

  // Check if it's a bcrypt hash (starts with $2a$, $2b$, $2x$, or $2y$)
  if (storedHash.match(/^\$2[abxy]\$/)) {
    try {
      const isValid = await bcrypt.compare(plainPassword, storedHash);
      return { isValid, hashType: 'bcrypt' };
    } catch (error) {
      console.error('Bcrypt verification failed:', error);
      return { isValid: false, hashType: 'bcrypt' };
    }
  }

  // Check if it's a SHA256 hash (64 characters, hex)
  if (storedHash.match(/^[a-f0-9]{64}$/i)) {
    try {
      const computedHash = await sha256Hash(plainPassword);
      const isValid = computedHash.toLowerCase() === storedHash.toLowerCase();
      return { isValid, hashType: 'sha256' };
    } catch (error) {
      console.error('SHA256 verification failed:', error);
      return { isValid: false, hashType: 'sha256' };
    }
  }

  // Check if it's plain text (fallback for development/testing)
  if (storedHash === plainPassword) {
    console.warn('Plain text password detected - this should not be used in production');
    return { isValid: true, hashType: 'plain' };
  }

  // Try bcrypt as fallback for any other format
  try {
    const isValid = await bcrypt.compare(plainPassword, storedHash);
    return { isValid, hashType: 'bcrypt' };
  } catch (error) {
    console.error('Password verification failed for unknown format:', error);
    return { isValid: false, hashType: 'unknown' };
  }
};
