import { supabase, setUserContext } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { format } from 'date-fns';
import {
  EnhancedKilnParameterNorm,
  KilnMeasurementAction,
  DailySummary,
  DailySummaryTimeSlot,
  KilnMonitoringMeasurement,
  KilnChamberZone,
  TIME_SLOTS,
  KILN_OPTIONS
} from '@/types/kilnMonitoring';

// Legacy interface for backward compatibility
export interface KilnParameterNorm {
  id: string;
  parameter_name: string;
  unit: string;
  min_value: number;
  max_value: number;
  cause: string;
  action: string;
  created_at: string;
  updated_at: string;
}

// Export the types that are needed by the hooks
export type { KilnMonitoringMeasurement, KilnChamberZone };

// Fetch all parameter norms (legacy)
export const getParameterNorms = async (userId?: string): Promise<KilnParameterNorm[]> => {
  if (userId) {
    await setUserContext(userId);
  }

  // Define the correct parameter order
  const parameterOrder = [
    'Pre-heat Zone 1 Temp',
    'Pre-heat Zone 2 Temp',
    'Pre-combustion Zone Temp',
    'Fire Zone Temp',
    'Cooling Zone 1 Temp',
    'Cooling Zone 2 Temp',
    'Fire Position in Chamber',
    'Fire Movement',
    'O2',
    'CO2',
    'Draught Pressure',
    'Fuel to Brick Ratio - Setting',
    'Brick Moisture % - Setting'
  ];

  // Parameters to exclude (old parameters that should not be shown)
  const excludeParameters = ['Brick Core Temp', 'CO', 'Brick Core Temp / CO', 'Brick Moisture'];

  try {
    const { data, error } = await supabase
      .from('kiln_parameter_norms')
      .select('*');

    if (error) {
      console.error('Error fetching parameter norms:', error);
      throw error;
    }

    if (data) {
      // Filter out excluded parameters
      const filteredData = data.filter(norm =>
        !excludeParameters.includes(norm.parameter_name)
      );

      // Sort according to the specified order
      const sortedData = filteredData.sort((a, b) => {
        const indexA = parameterOrder.indexOf(a.parameter_name);
        const indexB = parameterOrder.indexOf(b.parameter_name);

        // If parameter is not in the order list, put it at the end
        if (indexA === -1 && indexB === -1) return 0;
        if (indexA === -1) return 1;
        if (indexB === -1) return -1;

        return indexA - indexB;
      });

      return sortedData;
    }

    return [];
  } catch (error) {
    console.error('Error in getParameterNorms:', error);
    return []; // Return empty array to prevent component crash
  }
};

// Mock enhanced parameter norms data for testing
const MOCK_ENHANCED_PARAMETER_NORMS: EnhancedKilnParameterNorm[] = [
  {
    id: '1',
    parameter_name: 'Pre-heat Zone 1 Temp',
    unit: '°C',
    min_value: 200,
    max_value: 350,
    cause: 'Insufficient heat transfer or poor fuel distribution',
    action: 'Check fuel supply and adjust air flow',
    reasoning: 'Pre-heat zone 1 is critical for initial brick warming and moisture removal. Too low temperatures result in thermal shock, too high can cause rapid moisture expansion.',
    action_required: 'Monitor fuel flow rates and adjust primary air dampers',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',
    parameter_name: 'Pre-heat Zone 2 Temp',
    unit: '°C',
    min_value: 350,
    max_value: 500,
    cause: 'Heat transfer issues or combustion problems',
    action: 'Adjust fuel-air ratio and check heat exchangers',
    reasoning: 'Pre-heat zone 2 continues the gradual heating process. Proper temperature ensures even heat distribution and prevents cracking.',
    action_required: 'Verify secondary air settings and inspect heat transfer surfaces',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '3',
    parameter_name: 'Pre-combustion Zone Temp',
    unit: '°C',
    min_value: 500,
    max_value: 700,
    cause: 'Combustion air imbalance or fuel quality issues',
    action: 'Check combustion air supply and fuel quality',
    reasoning: 'Pre-combustion zone prepares bricks for the main firing process. Temperature control is crucial for proper chemical reactions.',
    action_required: 'Test fuel composition and adjust combustion air ratios',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '4',
    parameter_name: 'Fire Zone Temp',
    unit: '°C',
    min_value: 850,
    max_value: 950,
    cause: 'Fuel-air imbalance or combustion issues',
    action: 'Adjust fuel-air mixture and check combustion system',
    reasoning: 'Fire zone temperature is critical for proper brick firing. Too low results in underfired bricks, too high can cause deformation or cracking.',
    action_required: 'Monitor combustion parameters and adjust fuel-air ratio for optimal firing temperature',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '5',
    parameter_name: 'Cooling Zone 1 Temp',
    unit: '°C',
    min_value: 400,
    max_value: 600,
    cause: 'Insufficient cooling air or heat retention issues',
    action: 'Adjust cooling fans and check air circulation',
    reasoning: 'Cooling zone 1 begins the controlled cooling process. Too rapid cooling causes thermal stress and cracking.',
    action_required: 'Monitor cooling air flow rates and adjust fan speeds',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '6',
    parameter_name: 'Cooling Zone 2 Temp',
    unit: '°C',
    min_value: 200,
    max_value: 400,
    cause: 'Cooling system malfunction or air flow problems',
    action: 'Check cooling system operation and air ducts',
    reasoning: 'Cooling zone 2 completes the cooling process. Proper temperature ensures structural integrity of fired bricks.',
    action_required: 'Inspect cooling air distribution system and verify fan operation',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '7',
    parameter_name: 'Fire Position in Chamber',
    unit: 'm',
    min_value: 1,
    max_value: 24,
    cause: 'Fire advancement issues or fuel distribution problems',
    action: 'Check fuel feed system and fire progression',
    reasoning: 'Fire position indicates the burning front location in the chamber. Proper progression ensures even firing of all bricks.',
    action_required: 'Monitor fire advancement rate and adjust fuel distribution',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '8',
    parameter_name: 'Fire Movement',
    unit: 'm/day',
    min_value: 2.0,
    max_value: 4.0,
    cause: 'Fuel supply issues or draught problems',
    action: 'Adjust fuel rate and check draught system',
    reasoning: 'Fire movement rate affects firing quality and cycle time. Too slow wastes fuel, too fast causes uneven firing.',
    action_required: 'Calculate optimal fire speed based on brick type and chamber conditions',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '9',
    parameter_name: 'O2',
    unit: '%',
    min_value: 2,
    max_value: 6,
    cause: 'Excess air or combustion inefficiency',
    action: 'Adjust air-fuel ratio for optimal combustion',
    reasoning: 'Oxygen levels indicate combustion efficiency. Too high wastes energy, too low causes incomplete combustion.',
    action_required: 'Monitor and adjust primary and secondary air flows',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '10',
    parameter_name: 'CO2',
    unit: '%',
    min_value: 8,
    max_value: 12,
    cause: 'Combustion air imbalance or fuel quality',
    action: 'Optimize fuel-air mixture and check fuel quality',
    reasoning: 'Carbon dioxide levels reflect combustion completeness. Proper levels indicate efficient fuel utilization.',
    action_required: 'Analyze flue gas composition and adjust combustion parameters',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '11',
    parameter_name: 'Draught Pressure',
    unit: 'mmWC',
    min_value: -10,
    max_value: -2,
    cause: 'Fan operation issues or system blockages',
    action: 'Check fan operation and clear any blockages',
    reasoning: 'Draught pressure drives combustion air flow and flue gas removal. Proper pressure ensures stable combustion.',
    action_required: 'Inspect fan systems, dampers, and flue gas pathways',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '12',
    parameter_name: 'Fuel to Brick Ratio - Setting',
    unit: 'kg/1000 bricks',
    min_value: 80,
    max_value: 120,
    cause: 'Fuel efficiency issues or brick density variations',
    action: 'Optimize fuel consumption based on brick type and firing requirements',
    reasoning: 'Fuel to brick ratio determines firing cost and efficiency. Proper ratio ensures complete firing while minimizing fuel waste.',
    action_required: 'Calculate optimal fuel consumption based on brick specifications and firing curve',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '13',
    parameter_name: 'Brick Moisture % - Setting',
    unit: '%',
    min_value: 8,
    max_value: 12,
    cause: 'Drying process issues or storage conditions',
    action: 'Improve drying process and storage conditions',
    reasoning: 'Brick moisture content affects firing quality and energy consumption. Proper moisture prevents cracking and ensures even firing.',
    action_required: 'Monitor drying conditions and adjust storage environment',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Fetch enhanced parameter norms with additional fields
export const getEnhancedParameterNorms = async (userId?: string): Promise<EnhancedKilnParameterNorm[]> => {
  if (userId) {
    await setUserContext(userId);
  }

  // Define the correct parameter order
  const parameterOrder = [
    'Pre-heat Zone 1 Temp',
    'Pre-heat Zone 2 Temp',
    'Pre-combustion Zone Temp',
    'Fire Zone Temp',
    'Cooling Zone 1 Temp',
    'Cooling Zone 2 Temp',
    'Fire Position in Chamber',
    'Fire Movement',
    'O2',
    'CO2',
    'Draught Pressure',
    'Fuel to Brick Ratio - Setting',
    'Brick Moisture % - Setting'
  ];

  // Parameters to exclude (old parameters that should not be shown)
  const excludeParameters = ['Brick Core Temp', 'CO', 'Brick Core Temp / CO', 'Brick Moisture'];

  try {
    const { data, error } = await supabase
      .from('kiln_parameter_norms')
      .select('*');

    if (error) {
      console.error('Error fetching enhanced parameter norms, using mock data:', error);
      // Return mock data for testing when database is not available
      return MOCK_ENHANCED_PARAMETER_NORMS;
    }

    // If data exists but doesn't have the new fields, merge with mock data structure
    if (data && data.length > 0) {
      // Filter out excluded parameters
      const filteredData = data.filter(norm =>
        !excludeParameters.includes(norm.parameter_name)
      );

      // Sort according to the specified order
      const sortedData = filteredData.sort((a, b) => {
        const indexA = parameterOrder.indexOf(a.parameter_name);
        const indexB = parameterOrder.indexOf(b.parameter_name);

        // If parameter is not in the order list, put it at the end
        if (indexA === -1 && indexB === -1) return 0;
        if (indexA === -1) return 1;
        if (indexB === -1) return -1;

        return indexA - indexB;
      });

      return sortedData.map((norm, index) => ({
        ...norm,
        reasoning: norm.reasoning || MOCK_ENHANCED_PARAMETER_NORMS[index]?.reasoning || 'Parameter monitoring is essential for optimal kiln operation and product quality.',
        action_required: norm.action_required || MOCK_ENHANCED_PARAMETER_NORMS[index]?.action_required || 'Investigate cause and take corrective action based on parameter-specific procedures.'
      }));
    }

    return MOCK_ENHANCED_PARAMETER_NORMS;
  } catch (error) {
    console.error('Error in getEnhancedParameterNorms, using mock data:', error);
    return MOCK_ENHANCED_PARAMETER_NORMS; // Return mock data to prevent component crash
  }
};

// Update parameter norm (legacy)
export const updateParameterNorm = async (id: string, updates: Partial<KilnParameterNorm>) => {
  const { data, error } = await supabase
    .from('kiln_parameter_norms')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating parameter norm:', error);
    throw error;
  }

  return data;
};

// Update enhanced parameter norm
export const updateEnhancedParameterNorm = async (id: string, updates: Partial<EnhancedKilnParameterNorm>) => {
  const { data, error } = await supabase
    .from('kiln_parameter_norms')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating enhanced parameter norm:', error);
    throw error;
  }

  return data;
};

// Fetch measurements with optional filters
export const getMeasurements = async (filters?: {
  kiln_id?: string;
  date?: string;
  parameter?: string;
}, userId?: string): Promise<KilnMonitoringMeasurement[]> => {
  if (userId) {
    await setUserContext(userId);
  }
  try {
    let query = supabase
      .from('kiln_monitoring_measurements')
      .select('*')
      .order('measurement_date', { ascending: false })
      .order('measurement_time', { ascending: false });

    if (filters?.kiln_id) {
      query = query.eq('kiln_id', filters.kiln_id);
    }

    if (filters?.date) {
      query = query.eq('measurement_date', filters.date);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching measurements:', error);
      throw error;
    }

    // Transform the data to match our interface
    const transformedData: KilnMonitoringMeasurement[] = (data || []).map(item => ({
      id: item.id,
      kiln_id: item.kiln_id,
      chamber_number: item.chamber_number,
      fire_zone: item.fire_zone,
      measurement_time: item.measurement_time,
      measurement_date: item.measurement_date,
      parameters: (item.parameters as Record<string, number>) || {},
      user_id: item.user_id,
      created_at: item.created_at,
      updated_at: item.updated_at
    }));

    return transformedData;
  } catch (error) {
    console.error('Error in getMeasurements:', error);
    return []; // Return empty array to prevent component crash
  }
};

// Create new measurement
export const createMeasurement = async (measurement: Omit<KilnMonitoringMeasurement, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('kiln_monitoring_measurements')
    .insert(measurement)
    .select()
    .single();

  if (error) {
    console.error('Error creating measurement:', error);
    throw error;
  }

  return data;
};

// Create or update measurement action - using mock data since table doesn't exist in current schema
export const createMeasurementAction = async (action: Omit<KilnMeasurementAction, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    // For now, return mock data since the table doesn't exist
    console.log('Creating measurement action (mock):', action);
    return {
      id: `mock-${Date.now()}`,
      ...action,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error in createMeasurementAction, using mock response:', error);
    return {
      id: `mock-${Date.now()}`,
      ...action,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
};

// Update measurement action - using mock data since table doesn't exist in current schema
export const updateMeasurementAction = async (id: string, updates: Partial<KilnMeasurementAction>) => {
  try {
    // For now, return mock data since the table doesn't exist
    console.log('Updating measurement action (mock):', id, updates);
    return {
      id,
      ...updates,
      updated_at: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error in updateMeasurementAction, using mock response:', error);
    return {
      id,
      ...updates,
      updated_at: new Date().toISOString()
    };
  }
};

// Get measurement actions for a specific measurement - using mock data since table doesn't exist in current schema
export const getMeasurementActions = async (measurementId: string): Promise<KilnMeasurementAction[]> => {
  try {
    // For now, return empty array since the table doesn't exist
    console.log('Getting measurement actions (mock):', measurementId);
    return [];
  } catch (error) {
    console.error('Error in getMeasurementActions:', error);
    return [];
  }
};

// Mock daily summary data for testing
const createMockDailySummary = (date: string): DailySummary => {
  const timeSlots: DailySummaryTimeSlot[] = TIME_SLOTS.map(timeSlot => {
    const kilns: { [kilnId: string]: any } = {};

    // Add some mock data for demonstration
    if (['08:00', '12:00', '16:00', '20:00'].includes(timeSlot)) {
      KILN_OPTIONS.forEach(kiln => {
        const measurements: { [parameter: string]: any } = {};
        const testsPerformed: string[] = [];

        // Mock some parameter readings
        const mockParams = [
          { name: 'Brick Core Temp', value: 875 + Math.random() * 50, status: 'normal' as const },
          { name: 'Brick Moisture', value: 8.5 + Math.random() * 1.5, status: 'normal' as const },
          { name: 'CO', value: Math.random() * 0.15, status: Math.random() > 0.8 ? 'critical' as const : 'normal' as const },
          { name: 'CO₂', value: 13 + Math.random() * 2, status: 'normal' as const },
          { name: 'Cooling Zone Temp', value: 80 + Math.random() * 40, status: 'normal' as const }
        ];

        mockParams.forEach(param => {
          measurements[param.name] = {
            value: Math.round(param.value * 10) / 10,
            status: param.status,
            action_taken: param.status === 'critical' ? 'Adjusted air-fuel ratio' : undefined
          };
          testsPerformed.push(param.name);
        });

        kilns[kiln.id] = {
          kiln_name: kiln.name,
          measurements,
          tests_performed: testsPerformed,
          total_tests: testsPerformed.length
        };
      });
    }

    return {
      time_slot: timeSlot,
      kilns
    };
  });

  const totalMeasurements = timeSlots.reduce((count, slot) =>
    count + Object.keys(slot.kilns).length, 0);
  const parametersOutOfNorm = timeSlots.reduce((count, slot) => {
    return count + Object.values(slot.kilns).reduce((kilnCount, kiln: any) => {
      return kilnCount + Object.values(kiln.measurements).filter((m: any) => m.status === 'critical').length;
    }, 0);
  }, 0);
  const actionsTaken = timeSlots.reduce((count, slot) => {
    return count + Object.values(slot.kilns).reduce((kilnCount, kiln: any) => {
      return kilnCount + Object.values(kiln.measurements).filter((m: any) => m.action_taken).length;
    }, 0);
  }, 0);

  return {
    date,
    time_slots: timeSlots,
    total_measurements: totalMeasurements,
    parameters_out_of_norm: parametersOutOfNorm,
    actions_taken: actionsTaken
  };
};

// Get daily summary for a specific date
export const getDailySummary = async (date: string): Promise<DailySummary> => {
  try {
    // Fetch all measurements for the date
    const { data: measurements, error: measurementsError } = await supabase
      .from('kiln_monitoring_measurements')
      .select('*')
      .eq('measurement_date', date)
      .order('measurement_time');

    if (measurementsError) {
      console.error('Error fetching measurements for daily summary, using mock data:', measurementsError);
      return createMockDailySummary(date);
    }

    // Fetch parameter norms for comparison
    const { data: norms, error: normsError } = await supabase
      .from('kiln_parameter_norms')
      .select('*');

    if (normsError) {
      console.error('Error fetching norms for daily summary:', normsError);
      throw normsError;
    }

    // Process data into time slots
    const timeSlots: DailySummaryTimeSlot[] = TIME_SLOTS.map(timeSlot => {
      const slotMeasurements = (measurements || []).filter(m => m.measurement_time === timeSlot);

      const kilns: { [kilnId: string]: any } = {};

      KILN_OPTIONS.forEach(kiln => {
        const kilnMeasurements = slotMeasurements.filter(m => m.kiln_id === kiln.id);

        if (kilnMeasurements.length > 0) {
          const measurements: { [parameter: string]: any } = {};
          const testsPerformed: string[] = [];

          kilnMeasurements.forEach(measurement => {
            Object.entries(measurement.parameters as Record<string, number>).forEach(([param, value]) => {
              const norm = (norms || []).find(n => n.parameter_name.toLowerCase() === param.toLowerCase());
              let status: 'normal' | 'warning' | 'critical' = 'normal';

              if (norm && (value < norm.min_value || value > norm.max_value)) {
                status = 'critical';
              }

              measurements[param] = {
                value,
                status
              };

              if (!testsPerformed.includes(param)) {
                testsPerformed.push(param);
              }
            });
          });

          kilns[kiln.id] = {
            kiln_name: kiln.name,
            measurements,
            tests_performed: testsPerformed,
            total_tests: testsPerformed.length
          };
        }
      });

      return {
        time_slot: timeSlot,
        kilns
      };
    });

    const totalMeasurements = (measurements || []).length;
    const parametersOutOfNorm = timeSlots.reduce((count, slot) => {
      return count + Object.values(slot.kilns).reduce((kilnCount, kiln: any) => {
        return kilnCount + Object.values(kiln.measurements).filter((m: any) => m.status === 'critical').length;
      }, 0);
    }, 0);
    const actionsTaken = 0; // No actions data available

    return {
      date,
      time_slots: timeSlots,
      total_measurements: totalMeasurements,
      parameters_out_of_norm: parametersOutOfNorm,
      actions_taken: actionsTaken
    };
  } catch (error) {
    console.error('Error in getDailySummary, using mock data:', error);
    return createMockDailySummary(date);
  }
};

// Fetch chamber zones for all kilns
export const getChamberZones = async (): Promise<KilnChamberZone[]> => {
  try {
    const { data, error } = await supabase
      .from('kiln_chamber_zones')
      .select('*')
      .order('kiln_id')
      .order('chamber_number');

    if (error) {
      console.error('Error fetching chamber zones:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getChamberZones:', error);
    return []; // Return empty array to prevent component crash
  }
};

// Update chamber zone
export const updateChamberZone = async (kiln_id: string, chamber_number: number, zone: string) => {
  try {
    console.log(`🔄 Updating chamber zone: kiln=${kiln_id}, chamber=${chamber_number}, zone=${zone}`);

    // First, try to find existing record
    const { data: existing, error: findError } = await supabase
      .from('kiln_chamber_zones')
      .select('id')
      .eq('kiln_id', kiln_id)
      .eq('chamber_number', chamber_number)
      .single();

    if (findError && findError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('Error finding existing chamber zone:', findError);
      throw findError;
    }

    let result;
    if (existing) {
      // Update existing record
      console.log(`📝 Updating existing record with id: ${existing.id}`);
      const { data, error } = await supabase
        .from('kiln_chamber_zones')
        .update({
          zone,
          updated_at: new Date().toISOString()
        })
        .eq('id', existing.id)
        .select()
        .single();

      if (error) throw error;
      result = data;
    } else {
      // Insert new record
      console.log(`➕ Creating new record`);
      const { data, error } = await supabase
        .from('kiln_chamber_zones')
        .insert({
          kiln_id,
          chamber_number,
          zone,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      result = data;
    }

    console.log(`✅ Successfully updated chamber zone:`, result);
    return result;
  } catch (error) {
    console.error('❌ Error in updateChamberZone:', error);
    throw error;
  }
};

// Initialize chamber zones for a kiln if they don't exist
export const initializeChamberZones = async (kiln_id: string, chamber_count: number) => {
  try {
    const zones = Array.from({ length: chamber_count }, (_, i) => ({
      kiln_id,
      chamber_number: i + 1,
      zone: 'Inactive'
    }));

    const { data, error } = await supabase
      .from('kiln_chamber_zones')
      .upsert(zones, {
        onConflict: 'kiln_id,chamber_number',
        ignoreDuplicates: true
      })
      .select();

    if (error) {
      console.error('Error initializing chamber zones:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in initializeChamberZones:', error);
    throw error;
  }
};
