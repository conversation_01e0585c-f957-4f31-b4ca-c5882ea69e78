
import { supabase } from "@/integrations/supabase/client";

export const createHacklineCountsTable = async () => {
  try {
    // Since we can't call the RPC function directly, we'll check if the table exists
    // by trying to select from it
    const { data, error } = await supabase
      .from('hackline_counts')
      .select('id')
      .limit(1);

    if (error) {
      // If error indicates table doesn't exist, it means we need to create it
      // But we can't create tables from the client, so we'll log this
      console.error('hackline_counts table may not exist:', error);
      return { success: false, error: 'Table creation requires database migration' };
    }
    
    console.log('hackline_counts table exists and is accessible');
    return { success: true };
  } catch (error) {
    console.error('Unexpected error checking table:', error);
    return { success: false, error: 'Unexpected error occurred' };
  }
};
