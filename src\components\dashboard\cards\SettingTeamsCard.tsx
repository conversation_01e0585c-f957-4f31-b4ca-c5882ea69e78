
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getTeams } from "@/data/fuelBunkersData";
import { getExtrudedBrickTypes } from "@/data/managementBrickTypes";
import { getSettingSummary } from "@/data/settingProductionStore";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

interface SettingTeamsCardProps {
  onRecordProduction: () => void;
}

export const SettingTeamsCard = ({ onRecordProduction }: SettingTeamsCardProps) => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  const { data: teamsData = [], isLoading: isTeamsLoading } = useQuery({
    queryKey: ['teams'],
    queryFn: getTeams,
  });

  const { data: brickTypesData = [], isLoading: isBrickTypesLoading } = useQuery({
    queryKey: ['extrudedBrickTypes'],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getExtrudedBrickTypes(userId || undefined);
    },
  });

  const firesForSummary = [
    { id: "fire1", name: "Chamber 1" },
    { id: "fire2", name: "Chamber 2" },
  ];

  const { data: settingSummary = [], isLoading: isSummaryLoading } = useQuery({
    queryKey: ['settingTeamsCardSummary', brickTypesData],
    queryFn: () => getSettingSummary(
      (date) => date.toDateString() === new Date().toDateString(),
      firesForSummary,
      brickTypesData
    ),
    enabled: !!brickTypesData && brickTypesData.length > 0,
  });

  const totalPallets = settingSummary.reduce((sum, s) => sum + s.pallets, 0);
  const totalBricks = settingSummary.reduce((sum, s) => sum + s.bricks, 0);

  return (
    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-purple-800 flex items-center gap-2">
          <Users size={20} />
          Setting Teams
        </CardTitle>
        <p className="text-sm text-purple-600">Production Recording</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div>
            <p className="text-sm text-purple-600">Live Teams</p>
            <p className="text-xl font-bold text-purple-800">
              {isTeamsLoading ? "Loading..." : teamsData.length}
            </p>
          </div>
          <div>
            <p className="text-sm text-purple-600">Today's Output</p>
            <p className="text-lg font-medium text-purple-700">{totalPallets.toLocaleString()} pallets / {totalBricks.toLocaleString()} bricks</p>
          </div>
          <div className="w-full bg-purple-200 rounded-full h-2">
            <div
              className="bg-purple-600 h-2 rounded-full"
              style={{ width: `${Math.min((totalPallets / 200) * 100, 100)}%` }}
            ></div>
          </div>
          <Button 
            className="w-full bg-purple-500 hover:bg-purple-600"
            onClick={onRecordProduction}
          >
            Record Production
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
