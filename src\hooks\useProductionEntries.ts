import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { toast } from "sonner";

export interface ProductionEntry {
  id: number;
  created_at: string;
  date: string;
  pallet_count: number;
  brick_type_id: string;
  user_id: string;
}

export interface NewProductionEntry {
  date: string;
  pallet_count: number;
  brick_type_id: string;
}

export function useProductionEntries() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["productionEntries"],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        console.error('No authenticated user found for production entries query');
        return [];
      }

      // Use direct query instead of stored procedure to avoid type issues
      const { data, error } = await supabase
        .from('production_entries')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error("Error fetching production entries:", error);
        return [];
      }

      return (data || []) as ProductionEntry[];
    },
  });
}

export function useAddProductionEntry() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (newEntry: NewProductionEntry) => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      await setUserContext(userId);

      const { data, error } = await supabase
        .from("production_entries")
        .insert({
          ...newEntry,
          user_id: userId,
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding production entry:", error);
        throw new Error(error.message);
      }

      return data as ProductionEntry;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["productionEntries"] });
      toast.success("Production entry added successfully!");
    },
    onError: (error) => {
      console.error("Failed to add production entry:", error);
      toast.error("Failed to add production entry");
    },
  });
}

export function useSettingProductionEntries() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["settingProductionEntries"],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        console.error('No authenticated user found for setting production entries query');
        return [];
      }

      // Use direct query instead of stored procedure to avoid type issues
      const { data, error } = await supabase
        .from('setting_production_entries')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error("Error fetching setting production entries:", error);
        return [];
      }

      return (data || []) as any[];
    },
  });
}

export function useAddSettingProductionEntry() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (newEntry: NewProductionEntry) => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      await setUserContext(userId);

      const { data, error } = await supabase
        .from("setting_production_entries")
        .insert({
          date: newEntry.date,
          pallet_count: newEntry.pallet_count,
          brick_type_id: newEntry.brick_type_id,
          fire_id: (newEntry as any).fire_id,
          team_id: (newEntry as any).team_id,
          chamber_number: (newEntry as any).chamber_number,
          hour: (newEntry as any).hour,
          is_night_shift: (newEntry as any).is_night_shift,
          is_overtime: (newEntry as any).is_overtime
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding setting production entry:", error);
        throw new Error(error.message);
      }

      return data as any;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["settingProductionEntries"] });
      toast.success("Setting production entry added successfully!");
    },
    onError: (error) => {
      console.error("Failed to add setting production entry:", error);
      toast.error("Failed to add setting production entry");
    },
  });
}

export function useDehackingEntries() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["dehackingEntries"],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        console.error('No authenticated user found for dehacking entries query');
        return [];
      }

      // Use direct query instead of stored procedure to avoid type issues
      const { data, error } = await supabase
        .from('dehacking_entries')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error("Error fetching dehacking entries:", error);
        return [];
      }

      return (data || []);
    },
  });
}
