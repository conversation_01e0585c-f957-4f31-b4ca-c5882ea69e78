import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAllPalletMovements, createPalletMovement, recordPalletReturn, updatePalletMovementStatus, PalletStatus } from "@/data/palletTrackingData";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { toast } from "sonner";

export function usePalletMovements() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["pallet-movements"],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getAllPalletMovements(userId || undefined);
    },
  });
}

export function useCreatePalletMovement() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (input: {
      delivery_date: string;
      delivery_note: string;
      vehicle_registration: string;
      product_type: string;
      destination: string;
      pallets_loaded: number;
      client_name: string;
      comments?: string;
    }) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const result = await createPalletMovement({
        ...input,
        userId,
      });

      if (result.error) {
        throw new Error(result.error.message);
      }

      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pallet-movements"] });
      toast.success("Outgoing pallet recorded!");
    },
    onError: (error: any) => {
      toast.error("Could not record outgoing pallet: " + error?.message);
    },
  });
}

export function useRecordPalletReturn() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (input: {
      pallet_movement_id: string;
      return_date: string;
      pallets_returned: number;
      condition: string;
      comments?: string;
    }) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const result = await recordPalletReturn({
        ...input,
        userId,
      });

      if (result.error) {
        throw new Error(result.error.message);
      }

      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pallet-movements"] });
    },
  });
}

export function useUpdatePalletMovementStatus() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async ({ id, status }: { id: string; status: PalletStatus }) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      await updatePalletMovementStatus(id, status, userId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pallet-movements"] });
    },
  });
}
