
// Handles all Fuel Delivery data access.
import { supabase, setUserContext } from "@/integrations/supabase/client";

export interface FuelDeliveryInput {
  fuel_bunker_id: string;
  supplier: string;
  invoice_number: string;
  quantity: number;
  cost_per_liter: number;
  delivery_date: string; // ISO date string e.g. "2025-06-15"
}

// Returns { error, data }
export async function recordFuelDelivery(input: FuelDeliveryInput, userId?: string) {
  if (userId) {
    await setUserContext(userId);
  }

  const { data, error } = await supabase
    .from("fuel_deliveries")
    .insert([
      {
        fuel_bunker_id: input.fuel_bunker_id,
        supplier: input.supplier,
        invoice_number: input.invoice_number,
        quantity: input.quantity,
        cost_per_liter: input.cost_per_liter,
        delivery_date: input.delivery_date,
      },
    ])
    .select()
    .maybeSingle();
  return { data, error };
}
