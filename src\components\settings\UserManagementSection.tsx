
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Users, Plus, Edit, Trash2, Search } from "lucide-react";
import { useUsers, useCreateUser, useUpdateUser, useDeleteUser, User, type UserRole } from "@/hooks/useUsers";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialog<PERSON><PERSON><PERSON>, <PERSON>ertDialog<PERSON>rigger } from "@/components/ui/alert-dialog";

interface UserFormData {
  username: string;
  full_name: string;
  email?: string;
  password: string;
  role: UserRole;
  active: boolean;
}

export const UserManagementSection = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);

  const { data: users = [], isLoading } = useUsers();
  const createUserMutation = useCreateUser();
  const updateUserMutation = useUpdateUser();
  const deleteUserMutation = useDeleteUser();
  
  const addForm = useForm<UserFormData>({
    defaultValues: {
      active: true,
      role: "admin" as UserRole
    }
  });

  const editForm = useForm<UserFormData>({
    defaultValues: {
      active: true,
      role: "admin" as UserRole
    }
  });

  const filteredUsers = users.filter(user => 
    user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const onAddSubmit = async (data: UserFormData) => {
    try {
      await createUserMutation.mutateAsync({
        username: data.username,
        full_name: data.full_name,
        email: data.email,
        password: data.password,
        role: data.role
      });
      toast.success("User created successfully");
      setIsAddDialogOpen(false);
      addForm.reset();
    } catch (error) {
      console.error("Create user error:", error);
      toast.error(error instanceof Error ? error.message : "An error occurred");
    }
  };

  const onEditSubmit = async (data: UserFormData) => {
    if (!editingUser) return;
    
    try {
      await updateUserMutation.mutateAsync({
        id: editingUser.id,
        data: {
          username: data.username,
          full_name: data.full_name,
          email: data.email,
          role: data.role,
          active: data.active
        }
      });
      toast.success("User updated successfully");
      setEditingUser(null);
      editForm.reset();
    } catch (error) {
      console.error("Update user error:", error);
      toast.error(error instanceof Error ? error.message : "An error occurred");
    }
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    editForm.reset({
      username: user.username || "",
      full_name: user.full_name || "",
      email: user.email || "",
      password: "", // Don't populate password
      role: user.role,
      active: user.active
    });
  };

  const handleDelete = async (userId: string, userName: string) => {
    try {
      await deleteUserMutation.mutateAsync(userId);
      toast.success("User deleted successfully");
    } catch (error) {
      console.error("Delete user error:", error);
      const errorMessage = error instanceof Error ? error.message : "An error occurred";
      
      // Check if it's a foreign key constraint error
      if (errorMessage.includes("foreign key constraint") || errorMessage.includes("hackline_counts_user_id_fkey")) {
        toast.error(`Cannot delete ${userName}. This user has associated records (hackline counts) that must be removed first.`);
      } else {
        toast.error(`Failed to delete user: ${errorMessage}`);
      }
    }
  };

  const handleCloseEditDialog = () => {
    setEditingUser(null);
    editForm.reset();
  };

  const handleCloseAddDialog = () => {
    setIsAddDialogOpen(false);
    addForm.reset();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users size={20} />
          User Management
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={16} />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus size={16} className="mr-2" />
                Add User
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New User</DialogTitle>
              </DialogHeader>
              <form onSubmit={addForm.handleSubmit(onAddSubmit)} className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Username *</label>
                  <Input
                    {...addForm.register("username", { required: "Username is required" })}
                    placeholder="Enter username"
                  />
                  {addForm.formState.errors.username && <p className="text-red-500 text-sm">{addForm.formState.errors.username.message}</p>}
                </div>
                
                <div>
                  <label className="text-sm font-medium">Full Name *</label>
                  <Input
                    {...addForm.register("full_name", { required: "Full name is required" })}
                    placeholder="Enter full name"
                  />
                  {addForm.formState.errors.full_name && <p className="text-red-500 text-sm">{addForm.formState.errors.full_name.message}</p>}
                </div>
                
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <Input
                    {...addForm.register("email")}
                    type="email"
                    placeholder="Enter email (optional)"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">Password *</label>
                  <Input
                    {...addForm.register("password", { 
                      required: "Password is required",
                      minLength: { value: 6, message: "Password must be at least 6 characters" }
                    })}
                    type="password"
                    placeholder="Enter password"
                  />
                  {addForm.formState.errors.password && <p className="text-red-500 text-sm">{addForm.formState.errors.password.message}</p>}
                </div>
                
                <div>
                  <label className="text-sm font-medium">Role</label>
                  <Select value={addForm.watch("role")} onValueChange={(value: UserRole) => addForm.setValue("role", value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Administrator</SelectItem>
                      <SelectItem value="manager">Manager</SelectItem>
                      <SelectItem value="office_admin">Office Admin</SelectItem>
                      <SelectItem value="factory_supervisor">Factory Supervisor</SelectItem>
                      <SelectItem value="yard_supervisor">Yard Supervisor</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Active</label>
                  <Switch
                    checked={addForm.watch("active")}
                    onCheckedChange={(checked) => addForm.setValue("active", checked)}
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit" className="flex-1" disabled={createUserMutation.isPending}>
                    {createUserMutation.isPending ? "Creating..." : "Create User"}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={handleCloseAddDialog}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {isLoading ? (
          <div className="text-center py-4">Loading users...</div>
        ) : (
          <div className="space-y-2">
            {filteredUsers.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{user.full_name}</span>
                    <span className="text-sm text-slate-500">@{user.username}</span>
                    {!user.active && (
                      <span className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded">Inactive</span>
                    )}
                  </div>
                  <div className="text-sm text-slate-600">
                    {user.email} • {user.role}
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Dialog open={editingUser?.id === user.id} onOpenChange={(open) => !open && handleCloseEditDialog()}>
                    <DialogTrigger asChild>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleEdit(user)}
                      >
                        <Edit size={14} />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Edit User</DialogTitle>
                      </DialogHeader>
                      <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
                        <div>
                          <label className="text-sm font-medium">Username *</label>
                          <Input
                            {...editForm.register("username", { required: "Username is required" })}
                            placeholder="Enter username"
                          />
                          {editForm.formState.errors.username && <p className="text-red-500 text-sm">{editForm.formState.errors.username.message}</p>}
                        </div>
                        
                        <div>
                          <label className="text-sm font-medium">Full Name *</label>
                          <Input
                            {...editForm.register("full_name", { required: "Full name is required" })}
                            placeholder="Enter full name"
                          />
                          {editForm.formState.errors.full_name && <p className="text-red-500 text-sm">{editForm.formState.errors.full_name.message}</p>}
                        </div>
                        
                        <div>
                          <label className="text-sm font-medium">Email</label>
                          <Input
                            {...editForm.register("email")}
                            type="email"
                            placeholder="Enter email (optional)"
                          />
                        </div>
                        
                        <div>
                          <label className="text-sm font-medium">Role</label>
                          <Select value={editForm.watch("role")} onValueChange={(value: UserRole) => editForm.setValue("role", value)}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="admin">Administrator</SelectItem>
                              <SelectItem value="manager">Manager</SelectItem>
                              <SelectItem value="office_admin">Office Admin</SelectItem>
                              <SelectItem value="factory_supervisor">Factory Supervisor</SelectItem>
                              <SelectItem value="yard_supervisor">Yard Supervisor</SelectItem>
                              <SelectItem value="finance">Finance</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <label className="text-sm font-medium">Active</label>
                          <Switch
                            checked={editForm.watch("active")}
                            onCheckedChange={(checked) => editForm.setValue("active", checked)}
                          />
                        </div>
                        
                        <div className="flex gap-2">
                          <Button type="submit" className="flex-1" disabled={updateUserMutation.isPending}>
                            {updateUserMutation.isPending ? "Updating..." : "Update User"}
                          </Button>
                          <Button 
                            type="button" 
                            variant="outline" 
                            onClick={handleCloseEditDialog}
                          >
                            Cancel
                          </Button>
                        </div>
                      </form>
                    </DialogContent>
                  </Dialog>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button size="sm" variant="outline">
                        <Trash2 size={14} />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete User</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete {user.full_name}? This action cannot be undone.
                          {user.username === 'heinrichn' && (
                            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                              <strong>Note:</strong> This user may have associated records that need to be removed first.
                            </div>
                          )}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(user.id, user.full_name || '')}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ))}
            
            {filteredUsers.length === 0 && (
              <div className="text-center py-8 text-slate-500">
                No users found
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
