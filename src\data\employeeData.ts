
import { supabase, setUserContext } from "@/integrations/supabase/client";

export interface Employee {
  id: number;
  name: string;
  employee_code: string | null;
  role: string | null;
  department: string | null;
  status: string;
  performance: number;
}

export type EmployeeFormValues = {
  name: string;
  employeeCode: string;
  role: string;
  department: string;
};

export const getEmployees = async (userId?: string): Promise<Employee[]> => {
  if (userId) {
    // Use direct query instead of stored procedure to avoid type issues
    try {
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .order('name');

      if (error) {
        console.error("Error fetching employees:", error);
        throw new Error(error.message);
      }

      return data || [];
    } catch (error) {
      console.error("Failed to fetch employees:", error);
      throw error;
    }
  } else {
    // Fallback to direct query (may fail due to RLS)
    const { data, error } = await supabase.from('employees').select('*').order('name');
    if (error) {
      console.error("Error fetching employees with direct query:", error);
      throw new Error(error.message);
    }
    return data || [];
  }
};

export const addEmployee = async (employee: EmployeeFormValues, userId?: string) => {
  if (userId) {
    await setUserContext(userId);
  }

  const { data, error } = await supabase.from('employees').insert([
    {
        name: employee.name,
        employee_code: employee.employeeCode,
        role: employee.role,
        department: employee.department
    }
  ]).select().single();
  if (error) {
    console.error("Error adding employee:", error);
    throw error;
  }
  return data;
};

export const updateEmployee = async (employee: Partial<EmployeeFormValues> & { id: number }, userId?: string) => {
  if (userId) {
    await setUserContext(userId);
  }

  const { id, ...employeeData } = employee;

  const updateData = {
    name: employeeData.name,
    employee_code: employeeData.employeeCode,
    role: employeeData.role,
    department: employeeData.department
  };

  // remove undefined fields
  Object.keys(updateData).forEach(key => updateData[key as keyof typeof updateData] === undefined && delete updateData[key as keyof typeof updateData]);

  const { data, error } = await supabase.from('employees').update(updateData).eq('id', id).select().single();

  if (error) {
    console.error("Error updating employee:", error);
    throw error;
  }
  return data;
};

export const deleteEmployee = async (id: number, userId?: string) => {
  if (userId) {
    await setUserContext(userId);
  }

  const { error } = await supabase.from('employees').delete().eq('id', id);
  if (error) {
    console.error("Error deleting employee:", error);
    throw error;
  }
  return { id };
};
