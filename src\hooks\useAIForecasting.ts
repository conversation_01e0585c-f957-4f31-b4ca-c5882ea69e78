
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface ForecastParams {
  timeRange: number;
  type: string;
}

export const useAIForecasting = (params: ForecastParams) => {
  return useQuery({
    queryKey: ['ai-forecasting', params],
    queryFn: async () => {
      console.log('Calling AI forecasting with params:', params);
      
      const { data, error } = await supabase.functions.invoke('ai-business-intelligence', {
        body: { 
          type: 'forecasting',
          params
        }
      });

      if (error) {
        console.error('Error calling AI forecasting:', error);
        throw error;
      }

      return data?.data || {
        predictions: [],
        insights: [],
        confidence: 0
      };
    },
    enabled: !!params.timeRange && !!params.type,
    refetchInterval: 600000, // Refetch every 10 minutes
  });
};
