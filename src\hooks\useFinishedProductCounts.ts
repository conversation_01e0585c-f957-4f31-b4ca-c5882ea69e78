
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

export interface FinishedProductCount {
  id: number;
  created_at: string;
  date: string;
  pallet_count: number;
  product_type: string;
  user_id: string;
  notes?: string;
}

export interface NewFinishedProductCount {
  date: string;
  pallet_count: number;
  product_type: string;
  notes?: string;
}

export function useFinishedProductCounts() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["finishedProductCounts"],
    queryFn: async () => {
      // Set user context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { data, error } = await supabase
        .from("finished_product_counts")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching finished product counts:", error.message);
        throw new Error(error.message);
      }

      return data || [];
    },
  });
}

export function useAddFinishedProductCount() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (newCount: NewFinishedProductCount) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      // Set user context before database operation
      await setUserContext(userId);

      const { data, error } = await supabase
        .from("finished_product_counts")
        .insert({
          ...newCount,
          user_id: userId,
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding finished product count:", error.message);
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["finishedProductCounts"] });
    },
  });
}
