
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getTeamsWithMembers,
  getUnassignedEmployees,
  addTeam,
  deleteTeam,
  addTeamMember,
  removeTeamMember,
  getDehackingTeams,
  createDehackingTeams,
  getTeamMembers,
} from "@/data/teamData";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { toast } from "sonner";

export const useTeams = () => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ['teamsWithMembers'],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getTeamsWithMembers(userId || undefined);
    },
  });
};

export const useUnassignedEmployees = () => {
    const { currentUser } = useAuth();
    const { currentUser: userContextUser } = useUser();

    return useQuery({
        queryKey: ['unassignedEmployees'],
        queryFn: async () => {
          // Get effective user ID
          const userId = getEffectiveUserId(currentUser, userContextUser);
          return getUnassignedEmployees(userId || undefined);
        },
    });
}

export const useAddTeam = () => {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (name: string) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return addTeam(name, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      toast.success("Team added successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useDeleteTeam = () => {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (id: string) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return deleteTeam(id, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      toast.success("Team deleted successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useAddTeamMember = () => {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (params: { teamId: string, employeeId: number }) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return addTeamMember(params, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
      queryClient.invalidateQueries({ queryKey: ['unassignedEmployees'] });
      toast.success("Member added to team.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useRemoveTeamMember = () => {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (membershipId: string) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return removeTeamMember(membershipId, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
      queryClient.invalidateQueries({ queryKey: ['unassignedEmployees'] });
      toast.success("Member removed from team.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useDehackingTeams = () => {
  const queryClient = useQueryClient();

  return useQuery({
    queryKey: ['dehackingTeams'],
    queryFn: async () => {
      // First try to get existing teams
      const teams = await getDehackingTeams();

      // If no teams exist, create them
      if (teams.length === 0) {
        await createDehackingTeams();
        // Invalidate the main teams query to refresh the Team Management page
        queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
        // Fetch again after creation
        return await getDehackingTeams();
      }

      return teams;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useTeamMembers = (teamId: string | undefined) => {
  return useQuery({
    queryKey: ['teamMembers', teamId],
    queryFn: () => getTeamMembers(teamId || ''),
    enabled: !!teamId, // Only run query if teamId is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
