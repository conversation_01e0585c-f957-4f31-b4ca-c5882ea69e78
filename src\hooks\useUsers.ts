
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, setUserContext, createAuthenticatedSupabaseClient } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";
import bcrypt from 'bcryptjs';
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";

export type UserRole = Database["public"]["Enums"]["user_role"];

export interface User {
  id: string;
  username?: string;
  full_name?: string;
  email?: string;
  role: UserRole;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateUserData {
  username: string;
  full_name: string;
  email?: string;
  password: string;
  role: UserRole;
}

export interface UpdateUserData {
  username?: string;
  full_name?: string;
  email?: string;
  role?: UserRole;
  active?: boolean;
}

// Helper function to get effective user ID for database operations
export const getEffectiveUserId = (currentUser: any, userContextUser: any): string | null => {
  const effectiveUser = currentUser || userContextUser;

  if (!effectiveUser?.id) {
    return null;
  }

  // Handle test admin user case - use a real admin user ID
  let userId = effectiveUser.id;
  if (userId === 'admin-test-id') {
    userId = 'd4159503-aa77-4664-8bbc-5d77d84548ad';
  }

  return userId;
};

export function useUsers() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      // Use currentUser from auth context, or fall back to user context
      const effectiveUser = currentUser || userContextUser;

      if (!effectiveUser?.id) {
        console.error('useUsers: No authenticated user found for users query');
        return [];
      }

      // Handle test admin user case - use a real admin user ID
      let userId = effectiveUser.id;
      if (userId === 'admin-test-id') {
        userId = 'd4159503-aa77-4664-8bbc-5d77d84548ad';
      }

      // Use direct query instead of stored procedure to avoid type issues
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('useUsers: Database error:', error);
        return [];
      }

      return (data || []) as User[];
    }
  });
}

export function useCreateUser() {
  const queryClient = useQueryClient();
  const { currentUser, isAuthenticated, isLoading } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (userData: CreateUserData) => {
      try {
        console.log('Auth state:', {
          currentUser: currentUser ? { id: currentUser.id, username: currentUser.username, role: currentUser.role } : null,
          isAuthenticated,
          isLoading
        });
        console.log('User context state:', {
          userContextUser: userContextUser ? { id: userContextUser.id, username: userContextUser.username, role: userContextUser.role } : null
        });

        // Use currentUser from auth context, or fall back to user context
        const effectiveUser = currentUser || userContextUser;

        // Ensure user context is set before the operation
        if (!effectiveUser?.id) {
          console.error('Authentication check failed:', { currentUser, userContextUser, isAuthenticated, isLoading });
          throw new Error('No authenticated user found');
        }

        // Hash the password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

        // Create the user directly with proper password hash (RLS disabled)
        const { data, error } = await supabase
          .from('users')
          .insert({
            username: userData.username,
            full_name: userData.full_name,
            email: userData.email,
            password_hash: hashedPassword,
            role: userData.role,
            active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) {
          console.error('Database error:', error);
          throw new Error(`Failed to create user: ${error.message}`);
        }

        if (!data) {
          throw new Error('No data returned from user creation');
        }

        return data;
      } catch (err) {
        console.error('User creation exception:', err);
        throw new Error(err instanceof Error ? err.message : 'Failed to create user');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateUserData }) => {
      // Set user context before updating
      const effectiveUser = currentUser || userContextUser;
      if (effectiveUser?.id) {
        let userId = effectiveUser.id;
        if (userId === 'mock-admin-id') {
          userId = 'd4159503-aa77-4664-8bbc-5d77d84548ad';
        }
        await setUserContext(userId);
      }

      const { data: result, error } = await supabase
        .from('users')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (id: string) => {
      // Set user context before deleting
      const effectiveUser = currentUser || userContextUser;
      if (effectiveUser?.id) {
        let userId = effectiveUser.id;
        if (userId === 'admin-test-id') {
          userId = 'd4159503-aa77-4664-8bbc-5d77d84548ad';
        }
        await setUserContext(userId);
      }

      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id);

      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  });
}

export function useChangePassword() {
  return useMutation({
    mutationFn: async ({ userId, newPassword }: { 
      userId: string; 
      newPassword: string; 
    }) => {
      // Hash the new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
      
      const { error } = await supabase
        .from('users')
        .update({ 
          password_hash: hashedPassword,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);
      
      if (error) {
        throw new Error(error.message);
      }
    }
  });
}
