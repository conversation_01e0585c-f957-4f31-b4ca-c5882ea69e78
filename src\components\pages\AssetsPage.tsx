import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus<PERSON>ir<PERSON>, Edit, Trash2 } from "lucide-react";
import { AddAssetDialog } from "@/components/assets/AddAssetDialog";
import { EditAssetDialog } from "@/components/assets/EditAssetDialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { useToast } from "@/components/ui/use-toast";
import { v4 as uuidv4 } from "uuid";

// We'll need to update the Asset type to not require is_active
interface Asset {
  id: string;
  name: string;
  type: string;
  status: "active" | "out_of_service";
}

async function fetchAssets(userId?: string): Promise<Asset[]> {
  if (userId) {
    await setUserContext(userId);
  }

  const { data, error } = await supabase
    .from("assets")
    .select("*");
  if (error) throw new Error(error.message);
  return (data || []) as Asset[];
}

async function addAsset(newAsset: { name: string; type: string; identifier: string; status: "active" | "out_of_service" }, userId?: string) {
  if (userId) {
    await setUserContext(userId);
  }

  const id = uuidv4();
  const { name, type, identifier, status } = newAsset;
  const insertData: any = {
    id,
    name: identifier ? `${name} (${identifier})` : name,
    type,
    status
  };
  const { error } = await supabase
    .from("assets")
    .insert([insertData]);
  if (error) throw new Error(error.message);
}

async function updateAsset(updatedAsset: { id: string; name: string; type: string; identifier: string; status: "active" | "out_of_service" }, userId?: string) {
  if (userId) {
    await setUserContext(userId);
  }

  const name = updatedAsset.identifier ? `${updatedAsset.name} (${updatedAsset.identifier})` : updatedAsset.name;
  const { error } = await supabase
    .from("assets")
    .update({ name, type: updatedAsset.type, status: updatedAsset.status })
    .eq("id", updatedAsset.id);
  if (error) throw new Error(error.message);
}

async function deleteAsset(id: string, userId?: string) {
  if (userId) {
    await setUserContext(userId);
  }

  const { error } = await supabase
    .from("assets")
    .delete()
    .eq("id", id);
  if (error) throw new Error(error.message);
}

// Remove toggleAssetActive and all references to is_active

export const AssetsPage = () => {
  const [isAddAssetDialogOpen, setIsAddAssetDialogOpen] = useState(false);
  const [editDialog, setEditDialog] = useState<{ open: boolean; asset: Asset | null }>({ open: false, asset: null });
  const [statusFilter, setStatusFilter] = useState<"" | "active" | "out_of_service">("");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  const { data: assets, isLoading, isError } = useQuery({
    queryKey: ["assets"],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return fetchAssets(userId || undefined);
    },
  });

  const addAssetMutation = useMutation({
    mutationFn: (newAsset: { name: string; type: string; identifier: string; status: "active" | "out_of_service" }) => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return addAsset(newAsset, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["assets"] });
      setIsAddAssetDialogOpen(false);
      toast({
        title: "Asset Added.",
        description: "New asset was successfully added and persisted.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to add Asset",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const editAssetMutation = useMutation({
    mutationFn: (updatedAsset: { id: string; name: string; type: string; identifier: string; status: "active" | "out_of_service" }) => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return updateAsset(updatedAsset, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["assets"] });
      setEditDialog({ open: false, asset: null });
      toast({
        title: "Asset Updated.",
        description: "Asset details were updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update Asset",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteAssetMutation = useMutation({
    mutationFn: (id: string) => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return deleteAsset(id, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["assets"] });
      toast({ title: "Asset Deleted", description: "Asset deleted successfully." });
    },
    onError: (error: Error) => {
      toast({ title: "Failed to delete Asset", description: error.message, variant: "destructive" });
    },
  });

  // Removed toggleActiveMutation and handleToggleActive

  const handleAddAsset = (newAsset: { name: string; type: string; identifier: string; status: "active" | "out_of_service" }) => {
    addAssetMutation.mutate(newAsset);
  };

  const handleEditAsset = (updated: { id: string; name: string; type: string; identifier: string; status: "active" | "out_of_service" }) => {
    const { id, name, type, identifier, status } = updated;
    editAssetMutation.mutate({ id, name, type, identifier, status });
  };

  const handleDeleteAsset = (id: string) => {
    if (!window.confirm("Are you sure you want to delete this asset?")) return;
    deleteAssetMutation.mutate(id);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Manage Assets</h1>
        <Button onClick={() => setIsAddAssetDialogOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add New Asset
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Asset List</CardTitle>
          {/* Status Filter - Optionally keep or remove if not needed */}
          <div className="mt-2 flex gap-2 items-center">
            <label htmlFor="statusFilter" className="text-sm">Filter by Status:</label>
            <select
              id="statusFilter"
              className="border px-2 py-1 rounded text-sm"
              value={statusFilter}
              onChange={e => setStatusFilter(e.target.value as any)}
            >
              <option value="">All</option>
              <option value="active">Active</option>
              <option value="out_of_service">Out of Service</option>
            </select>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="py-6 text-center text-muted-foreground">Loading assets…</div>
          ) : isError ? (
            <div className="py-6 text-center text-red-500">Could not load assets.</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Asset Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Identifier</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {assets && assets.filter(asset => statusFilter === "" || asset.status === statusFilter).length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center text-muted-foreground">
                      No assets found.
                    </TableCell>
                  </TableRow>
                ) : (
                  assets?.filter(asset => statusFilter === "" || asset.status === statusFilter).map((asset) => {
                    let displayName = asset.name;
                    let identifier = "";
                    const match = displayName.match(/^(.*) \((.+)\)$/);
                    if (match) {
                      displayName = match[1];
                      identifier = match[2];
                    }
                    return (
                      <TableRow key={asset.id}>
                        <TableCell className="font-medium">{displayName}</TableCell>
                        <TableCell>{asset.type}</TableCell>
                        <TableCell>{identifier}</TableCell>
                        <TableCell>
                          <AssetStatusBadge status={asset.status} />
                        </TableCell>
                        <TableCell className="flex gap-2 items-center">
                          <Button
                            size="icon"
                            variant="ghost"
                            aria-label="Edit"
                            onClick={() => setEditDialog({ open: true, asset })}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            aria-label="Delete"
                            onClick={() => handleDeleteAsset(asset.id)}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <AddAssetDialog
        isOpen={isAddAssetDialogOpen}
        onClose={() => setIsAddAssetDialogOpen(false)}
        onAddAsset={handleAddAsset}
        isLoading={addAssetMutation.isPending}
      />

      <EditAssetDialog
        isOpen={editDialog.open}
        onClose={() => setEditDialog({ open: false, asset: null })}
        asset={editDialog.asset}
        isLoading={editAssetMutation.isPending}
        onEditAsset={handleEditAsset}
      />
    </div>
  );
};

// Helper badge component for status
import { Badge } from "@/components/ui/badge";
function AssetStatusBadge({ status }: { status: "active" | "out_of_service" }) {
  return (
    <Badge variant={status === "active" ? "default" : "secondary"} className={status === "out_of_service" ? "bg-red-500 text-white" : "bg-green-500 text-white"}>
      {status === "active" ? "Active" : "Out of Service"}
    </Badge>
  );
}
