
import { 
  format, 
  startOfDay, 
  endOfDay, 
  startOfWeek, 
  endOfWeek, 
  startOfMonth, 
  endOfMonth,
  addDays,
  addWeeks,
  addMonths,
  subDays,
  subWeeks,
  subMonths,
  isSameDay,
  isWithinInterval
} from 'date-fns';
import { ViewMode, LoadPlanningEntry } from '@/types/loadPlanning';

export interface DateRange {
  start: Date;
  end: Date;
}

/**
 * Get the date range for a given view mode and current date
 */
export const getDateRangeForView = (currentDate: Date, viewMode: ViewMode): DateRange => {
  switch (viewMode) {
    case 'daily':
      return {
        start: startOfDay(currentDate),
        end: endOfDay(currentDate)
      };
    case 'weekly':
      return {
        start: startOfWeek(currentDate, { weekStartsOn: 1 }), // Monday
        end: endOfWeek(currentDate, { weekStartsOn: 1 }) // Sunday
      };
    case 'monthly':
      return {
        start: startOfMonth(currentDate),
        end: endOfMonth(currentDate)
      };
    default:
      return {
        start: startOfDay(currentDate),
        end: endOfDay(currentDate)
      };
  }
};

/**
 * Navigate to the next period based on view mode
 */
export const getNextPeriod = (currentDate: Date, viewMode: ViewMode): Date => {
  switch (viewMode) {
    case 'daily':
      return addDays(currentDate, 1);
    case 'weekly':
      return addWeeks(currentDate, 1);
    case 'monthly':
      return addMonths(currentDate, 1);
    default:
      return addDays(currentDate, 1);
  }
};

/**
 * Navigate to the previous period based on view mode
 */
export const getPreviousPeriod = (currentDate: Date, viewMode: ViewMode): Date => {
  switch (viewMode) {
    case 'daily':
      return subDays(currentDate, 1);
    case 'weekly':
      return subWeeks(currentDate, 1);
    case 'monthly':
      return subMonths(currentDate, 1);
    default:
      return subDays(currentDate, 1);
  }
};

/**
 * Filter loads based on the current date range and view mode
 */
export const filterLoadsByDateRange = (
  loads: LoadPlanningEntry[], 
  currentDate: Date, 
  viewMode: ViewMode
): LoadPlanningEntry[] => {
  const dateRange = getDateRangeForView(currentDate, viewMode);
  
  return loads.filter(load => {
    const loadDate = new Date(load.date);
    return isWithinInterval(loadDate, dateRange);
  });
};

/**
 * Get a formatted title for the current period
 */
export const getPeriodTitle = (currentDate: Date, viewMode: ViewMode): string => {
  switch (viewMode) {
    case 'daily':
      return format(currentDate, 'EEEE, MMMM d, yyyy');
    case 'weekly':
      const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 });
      const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 });
      return `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`;
    case 'monthly':
      return format(currentDate, 'MMMM yyyy');
    default:
      return format(currentDate, 'EEEE, MMMM d, yyyy');
  }
};

/**
 * Check if a date is today
 */
export const isToday = (date: Date): boolean => {
  return isSameDay(date, new Date());
};

/**
 * Get loads for a specific day (used in weekly and monthly views)
 */
export const getLoadsForDay = (loads: LoadPlanningEntry[], day: Date): LoadPlanningEntry[] => {
  return loads.filter(load => 
    format(new Date(load.date), 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd')
  ).sort((a, b) => (a.rank || 1) - (b.rank || 1)); // Sort by rank (priority)
};
