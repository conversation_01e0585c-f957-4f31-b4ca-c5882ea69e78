
import { supabase, setUserContext } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Use database enums for status fields
type KilnStatus = Database["public"]["Enums"]["kiln_status"];
type FireStatus = Database["public"]["Enums"]["fire_status"];

// Fix type definitions for matching DB exactly
export interface FireConfig {
  id: string;
  name: string;
  kiln_id: string;
  status: FireStatus;
}
export interface KilnConfig {
  id: string;
  name: string;
  status: KilnStatus;
  fires: FireConfig[];
}

export const getKilns = async (userId?: string): Promise<KilnConfig[]> => {
  if (userId) {
    await setUserContext(userId);
  }
  const { data, error } = await supabase
    .from('kilns')
    .select(`
      id,
      name,
      status,
      fires (
        id,
        name,
        status,
        kiln_id
      )
    `);

  if (error) {
    console.error("Error fetching kilns with fires:", error);
    throw new Error(error.message);
  }

  if (!data) {
    return [];
  }

  // The data is already in the desired shape, just need to assert the type
  const kilns: KilnConfig[] = data.map(kiln => ({
    id: kiln.id,
    name: kiln.name,
    status: kiln.status as KilnStatus,
    fires: kiln.fires.map(fire => ({
      id: fire.id,
      name: fire.name,
      status: fire.status as FireStatus,
      kiln_id: fire.kiln_id,
    })),
  }));

  console.log("[DEBUG] Kilns loaded from Supabase:", kilns);
  return kilns;
};

export const updateKilnStatus = async ({
  id,
  status,
  userId,
}: {
  id: string;
  status: KilnStatus;
  userId?: string;
}) => {
  if (userId) {
    await setUserContext(userId);
  }
  console.log("[DEBUG] Updating kiln status:", { id, status });

  // First check if the kiln exists using regular client
  const { data: existingKiln, error: checkError } = await supabase
    .from("kilns")
    .select("id, name, status")
    .eq("id", id)
    .single();

  if (checkError) {
    console.error("Error checking kiln existence:", checkError);
    throw new Error(`Kiln not found: ${checkError.message}`);
  }

  if (!existingKiln) {
    console.error("No kiln found with ID:", id);
    throw new Error("No kiln found with the specified ID");
  }

  console.log("[DEBUG] Existing kiln found:", existingKiln);

  // Use regular client to update the kiln (RLS policies will handle permissions)
  const { data, error } = await supabase
    .from("kilns")
    .update({ status })
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating kiln status:", error);
    throw new Error(error.message);
  }

  console.log("[DEBUG] Kiln updated successfully:", data);
  return data;
};

export const getFires = async (userId?: string): Promise<FireConfig[]> => {
  if (userId) {
    await setUserContext(userId);
  }
  // Just fetch id, name, status, kiln_id from the fires table
  const { data, error } = await supabase
    .from("fires")
    .select("id, name, status, kiln_id");

  if (error) {
    console.error("Error fetching fires:", error);
    throw new Error(error.message);
  }

  return (data ?? []).map((fire: any) => ({
    id: fire.id,
    name: fire.name,
    status: fire.status,
    kiln_id: fire.kiln_id,
  }));
};
