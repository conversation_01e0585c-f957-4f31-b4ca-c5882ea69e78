export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      assets: {
        Row: {
          id: string
          name: string
          status: string
          type: string
        }
        Insert: {
          id: string
          name: string
          status?: string
          type: string
        }
        Update: {
          id?: string
          name?: string
          status?: string
          type?: string
        }
        Relationships: []
      }
      audit_logs: {
        Row: {
          action: string
          created_at: string
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          record_id: string | null
          table_name: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action: string
          created_at?: string
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id?: string | null
          table_name?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string
          created_at?: string
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id?: string | null
          table_name?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      breakdowns: {
        Row: {
          breakdown_comment: string
          created_at: string
          date: string
          id: number
          start_time: string | null
          stop_time: string
          time: string
          user_id: string
        }
        Insert: {
          breakdown_comment: string
          created_at?: string
          date: string
          id?: number
          start_time?: string | null
          stop_time: string
          time: string
          user_id: string
        }
        Update: {
          breakdown_comment?: string
          created_at?: string
          date?: string
          id?: number
          start_time?: string | null
          stop_time?: string
          time?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "breakdowns_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      carbon_tests: {
        Row: {
          average_carbon: number
          average_sulphur: number
          created_at: string
          date: string
          id: number
          test_type: string
          time: string
          user_id: string
        }
        Insert: {
          average_carbon: number
          average_sulphur: number
          created_at?: string
          date: string
          id?: number
          test_type: string
          time: string
          user_id: string
        }
        Update: {
          average_carbon?: number
          average_sulphur?: number
          created_at?: string
          date?: string
          id?: number
          test_type?: string
          time?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "carbon_tests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      chamber_fire_status: {
        Row: {
          chamber_number: number
          created_at: string
          fire_id: string | null
          id: number
          is_burning: boolean
          kiln_id: string
          updated_at: string
          updated_by: string | null
        }
        Insert: {
          chamber_number: number
          created_at?: string
          fire_id?: string | null
          id?: number
          is_burning?: boolean
          kiln_id: string
          updated_at?: string
          updated_by?: string | null
        }
        Update: {
          chamber_number?: number
          created_at?: string
          fire_id?: string | null
          id?: number
          is_burning?: boolean
          kiln_id?: string
          updated_at?: string
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chamber_fire_status_fire_id_fkey"
            columns: ["fire_id"]
            isOneToOne: false
            referencedRelation: "fires"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chamber_fire_status_kiln_id_fkey"
            columns: ["kiln_id"]
            isOneToOne: false
            referencedRelation: "kilns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chamber_fire_status_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      dehacking_entries: {
        Row: {
          brick_type_id: string
          chamber_number: number | null
          created_at: string
          date: string
          employee_id: number
          fire_id: string | null
          hour: number
          id: number
          is_night_shift: boolean | null
          is_overtime: boolean | null
          pallet_count: number
        }
        Insert: {
          brick_type_id: string
          chamber_number?: number | null
          created_at?: string
          date: string
          employee_id: number
          fire_id?: string | null
          hour: number
          id?: number
          is_night_shift?: boolean | null
          is_overtime?: boolean | null
          pallet_count: number
        }
        Update: {
          brick_type_id?: string
          chamber_number?: number | null
          created_at?: string
          date?: string
          employee_id?: number
          fire_id?: string | null
          hour?: number
          id?: number
          is_night_shift?: boolean | null
          is_overtime?: boolean | null
          pallet_count?: number
        }
        Relationships: [
          {
            foreignKeyName: "dehacking_entries_brick_type_id_fkey"
            columns: ["brick_type_id"]
            isOneToOne: false
            referencedRelation: "management_brick_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "dehacking_entries_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "dehacking_entries_fire_id_fkey"
            columns: ["fire_id"]
            isOneToOne: false
            referencedRelation: "fires"
            referencedColumns: ["id"]
          },
        ]
      }
      employee_roles: {
        Row: {
          employee_id: number
          id: number
          role: string
        }
        Insert: {
          employee_id: number
          id?: number
          role: string
        }
        Update: {
          employee_id?: number
          id?: number
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "employee_roles_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      employees: {
        Row: {
          department: string | null
          employee_code: string | null
          id: number
          name: string
          performance: number
          role: string | null
          status: string
        }
        Insert: {
          department?: string | null
          employee_code?: string | null
          id?: number
          name: string
          performance?: number
          role?: string | null
          status?: string
        }
        Update: {
          department?: string | null
          employee_code?: string | null
          id?: number
          name?: string
          performance?: number
          role?: string | null
          status?: string
        }
        Relationships: []
      }
      finished_product_counts: {
        Row: {
          created_at: string
          date: string
          id: number
          notes: string | null
          pallet_count: number
          product_type: string
          user_id: string
        }
        Insert: {
          created_at?: string
          date: string
          id?: number
          notes?: string | null
          pallet_count: number
          product_type: string
          user_id: string
        }
        Update: {
          created_at?: string
          date?: string
          id?: number
          notes?: string | null
          pallet_count?: number
          product_type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "finished_product_counts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      fire_comments: {
        Row: {
          comment: string
          created_at: string
          date: string
          fire_id: string
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          comment: string
          created_at?: string
          date: string
          fire_id: string
          id?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          comment?: string
          created_at?: string
          date?: string
          fire_id?: string
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fire_comments_fire_id_fkey"
            columns: ["fire_id"]
            isOneToOne: false
            referencedRelation: "fires"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fire_comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      fires: {
        Row: {
          id: string
          kiln_id: string
          name: string
          status: Database["public"]["Enums"]["fire_status"]
        }
        Insert: {
          id: string
          kiln_id: string
          name: string
          status: Database["public"]["Enums"]["fire_status"]
        }
        Update: {
          id?: string
          kiln_id?: string
          name?: string
          status?: Database["public"]["Enums"]["fire_status"]
        }
        Relationships: [
          {
            foreignKeyName: "fires_kiln_id_fkey"
            columns: ["kiln_id"]
            isOneToOne: false
            referencedRelation: "kilns"
            referencedColumns: ["id"]
          },
        ]
      }
      forklift_allocations: {
        Row: {
          allocated_teams: string[]
          allocation_type: string
          end_date: string | null
          forklift_driver_id: number
          id: number
          is_active: boolean
          start_date: string
        }
        Insert: {
          allocated_teams: string[]
          allocation_type: string
          end_date?: string | null
          forklift_driver_id: number
          id?: number
          is_active?: boolean
          start_date?: string
        }
        Update: {
          allocated_teams?: string[]
          allocation_type?: string
          end_date?: string | null
          forklift_driver_id?: number
          id?: number
          is_active?: boolean
          start_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "forklift_allocations_forklift_driver_id_fkey"
            columns: ["forklift_driver_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      fuel_bunkers: {
        Row: {
          capacity: number
          current_level: number
          id: string
          name: string
        }
        Insert: {
          capacity: number
          current_level: number
          id: string
          name: string
        }
        Update: {
          capacity?: number
          current_level?: number
          id?: string
          name?: string
        }
        Relationships: []
      }
      fuel_deliveries: {
        Row: {
          cost_per_liter: number
          created_at: string
          delivery_date: string
          fuel_bunker_id: string
          id: string
          invoice_number: string
          quantity: number
          supplier: string
        }
        Insert: {
          cost_per_liter: number
          created_at?: string
          delivery_date: string
          fuel_bunker_id: string
          id?: string
          invoice_number: string
          quantity: number
          supplier: string
        }
        Update: {
          cost_per_liter?: number
          created_at?: string
          delivery_date?: string
          fuel_bunker_id?: string
          id?: string
          invoice_number?: string
          quantity?: number
          supplier?: string
        }
        Relationships: [
          {
            foreignKeyName: "fuel_deliveries_fuel_bunker_id_fkey"
            columns: ["fuel_bunker_id"]
            isOneToOne: false
            referencedRelation: "fuel_bunkers"
            referencedColumns: ["id"]
          },
        ]
      }
      fuel_dispensing_transactions: {
        Row: {
          asset_id: string
          created_at: string
          ending_reading: number | null
          fuel_bunker_id: string
          id: string
          notes: string | null
          operator_id: number
          quantity_liters: number
          starting_reading: number | null
          transaction_date: string
        }
        Insert: {
          asset_id: string
          created_at?: string
          ending_reading?: number | null
          fuel_bunker_id: string
          id?: string
          notes?: string | null
          operator_id: number
          quantity_liters: number
          starting_reading?: number | null
          transaction_date?: string
        }
        Update: {
          asset_id?: string
          created_at?: string
          ending_reading?: number | null
          fuel_bunker_id?: string
          id?: string
          notes?: string | null
          operator_id?: number
          quantity_liters?: number
          starting_reading?: number | null
          transaction_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "fuel_dispensing_transactions_asset_id_fkey"
            columns: ["asset_id"]
            isOneToOne: false
            referencedRelation: "assets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fuel_dispensing_transactions_fuel_bunker_id_fkey"
            columns: ["fuel_bunker_id"]
            isOneToOne: false
            referencedRelation: "fuel_bunkers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fuel_dispensing_transactions_operator_id_fkey"
            columns: ["operator_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      hackline_counts: {
        Row: {
          count_total: number
          created_at: string
          date: string
          id: number
          notes: string | null
          pallet_count: number
          pallet_type: string
          user_id: string
        }
        Insert: {
          count_total: number
          created_at?: string
          date: string
          id?: number
          notes?: string | null
          pallet_count?: number
          pallet_type?: string
          user_id: string
        }
        Update: {
          count_total?: number
          created_at?: string
          date?: string
          id?: number
          notes?: string | null
          pallet_count?: number
          pallet_type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "hackline_counts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      hourly_rate_records: {
        Row: {
          created_at: string
          date: string
          employee_id: number
          hourly_rate: number
          hours_worked: number
          id: string
          start_time: string
          stop_time: string
          team_id: string
          total_pay: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          date: string
          employee_id: number
          hourly_rate?: number
          hours_worked: number
          id?: string
          start_time: string
          stop_time: string
          team_id: string
          total_pay: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          date?: string
          employee_id?: number
          hourly_rate?: number
          hours_worked?: number
          id?: string
          start_time?: string
          stop_time?: string
          team_id?: string
          total_pay?: number
          updated_at?: string
        }
        Relationships: []
      }
      kiln_chamber_zones: {
        Row: {
          chamber_number: number
          created_at: string
          id: string
          kiln_id: string
          updated_at: string
          zone: string
        }
        Insert: {
          chamber_number: number
          created_at?: string
          id?: string
          kiln_id: string
          updated_at?: string
          zone?: string
        }
        Update: {
          chamber_number?: number
          created_at?: string
          id?: string
          kiln_id?: string
          updated_at?: string
          zone?: string
        }
        Relationships: []
      }
      kiln_measurement_actions: {
        Row: {
          action_by: string | null
          action_date: string | null
          action_required: string | null
          action_taken: string | null
          created_at: string
          id: string
          is_out_of_norm: boolean
          measurement_id: string
          parameter_name: string
          reading_value: number
          reasoning: string | null
          updated_at: string
        }
        Insert: {
          action_by?: string | null
          action_date?: string | null
          action_required?: string | null
          action_taken?: string | null
          created_at?: string
          id?: string
          is_out_of_norm?: boolean
          measurement_id: string
          parameter_name: string
          reading_value: number
          reasoning?: string | null
          updated_at?: string
        }
        Update: {
          action_by?: string | null
          action_date?: string | null
          action_required?: string | null
          action_taken?: string | null
          created_at?: string
          id?: string
          is_out_of_norm?: boolean
          measurement_id?: string
          parameter_name?: string
          reading_value?: number
          reasoning?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      kiln_monitoring_measurements: {
        Row: {
          chamber_number: number
          created_at: string
          fire_zone: string | null
          id: string
          kiln_id: string
          measurement_date: string
          measurement_time: string
          parameters: Json
          updated_at: string
          user_id: string | null
        }
        Insert: {
          chamber_number: number
          created_at?: string
          fire_zone?: string | null
          id?: string
          kiln_id: string
          measurement_date?: string
          measurement_time: string
          parameters: Json
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          chamber_number?: number
          created_at?: string
          fire_zone?: string | null
          id?: string
          kiln_id?: string
          measurement_date?: string
          measurement_time?: string
          parameters?: Json
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      kiln_parameter_norms: {
        Row: {
          action: string
          action_required: string | null
          cause: string
          created_at: string
          id: string
          last_action_by: string | null
          last_action_date: string | null
          last_action_taken: string | null
          max_value: number
          min_value: number
          parameter_name: string
          reasoning: string | null
          unit: string
          updated_at: string
        }
        Insert: {
          action: string
          action_required?: string | null
          cause: string
          created_at?: string
          id?: string
          last_action_by?: string | null
          last_action_date?: string | null
          last_action_taken?: string | null
          max_value: number
          min_value: number
          parameter_name: string
          reasoning?: string | null
          unit: string
          updated_at?: string
        }
        Update: {
          action?: string
          action_required?: string | null
          cause?: string
          created_at?: string
          id?: string
          last_action_by?: string | null
          last_action_date?: string | null
          last_action_taken?: string | null
          max_value?: number
          min_value?: number
          parameter_name?: string
          reasoning?: string | null
          unit?: string
          updated_at?: string
        }
        Relationships: []
      }
      kiln_temperature_readings: {
        Row: {
          created_at: string
          fire_id: string | null
          id: string
          kiln_id: string
          notes: string | null
          reading_time: string
          recorded_by: string | null
          temperature: number
        }
        Insert: {
          created_at?: string
          fire_id?: string | null
          id?: string
          kiln_id: string
          notes?: string | null
          reading_time?: string
          recorded_by?: string | null
          temperature: number
        }
        Update: {
          created_at?: string
          fire_id?: string | null
          id?: string
          kiln_id?: string
          notes?: string | null
          reading_time?: string
          recorded_by?: string | null
          temperature?: number
        }
        Relationships: []
      }
      kilns: {
        Row: {
          id: string
          name: string
          status: Database["public"]["Enums"]["kiln_status"]
        }
        Insert: {
          id: string
          name: string
          status: Database["public"]["Enums"]["kiln_status"]
        }
        Update: {
          id?: string
          name?: string
          status?: Database["public"]["Enums"]["kiln_status"]
        }
        Relationships: []
      }
      load_planning: {
        Row: {
          brick_count: number | null
          brick_type_id: string | null
          client_name: string
          created_at: string
          date: string
          dispatched: boolean | null
          dispatched_at: string | null
          dispatched_by: string | null
          id: string
          load_description: string | null
          load_type: string
          rank: number | null
          ready: boolean
          transporter: string
          updated_at: string
          user_id: string
        }
        Insert: {
          brick_count?: number | null
          brick_type_id?: string | null
          client_name: string
          created_at?: string
          date: string
          dispatched?: boolean | null
          dispatched_at?: string | null
          dispatched_by?: string | null
          id?: string
          load_description?: string | null
          load_type: string
          rank?: number | null
          ready?: boolean
          transporter: string
          updated_at?: string
          user_id: string
        }
        Update: {
          brick_count?: number | null
          brick_type_id?: string | null
          client_name?: string
          created_at?: string
          date?: string
          dispatched?: boolean | null
          dispatched_at?: string | null
          dispatched_by?: string | null
          id?: string
          load_description?: string | null
          load_type?: string
          rank?: number | null
          ready?: boolean
          transporter?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "load_planning_brick_type_id_fkey"
            columns: ["brick_type_id"]
            isOneToOne: false
            referencedRelation: "management_brick_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "load_planning_dispatched_by_fkey"
            columns: ["dispatched_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      management_brick_types: {
        Row: {
          brick_stage: string
          bricks_per_pallet: number
          category: string
          dehacking_day_rate: number | null
          dehacking_night_rate: number | null
          dehacking_rate: number
          grade: string
          id: string
          name: string
          overtime_rate: number
          setting_day_rate: number | null
          setting_night_rate: number | null
          setting_rate: number
          status: string
        }
        Insert: {
          brick_stage?: string
          bricks_per_pallet: number
          category: string
          dehacking_day_rate?: number | null
          dehacking_night_rate?: number | null
          dehacking_rate: number
          grade: string
          id: string
          name: string
          overtime_rate: number
          setting_day_rate?: number | null
          setting_night_rate?: number | null
          setting_rate: number
          status: string
        }
        Update: {
          brick_stage?: string
          bricks_per_pallet?: number
          category?: string
          dehacking_day_rate?: number | null
          dehacking_night_rate?: number | null
          dehacking_rate?: number
          grade?: string
          id?: string
          name?: string
          overtime_rate?: number
          setting_day_rate?: number | null
          setting_night_rate?: number | null
          setting_rate?: number
          status?: string
        }
        Relationships: []
      }
      notification_settings: {
        Row: {
          created_at: string | null
          email_notifications: boolean | null
          employee_updates: boolean | null
          fuel_level_warnings: boolean | null
          id: string
          production_alerts: boolean | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          email_notifications?: boolean | null
          employee_updates?: boolean | null
          fuel_level_warnings?: boolean | null
          id?: string
          production_alerts?: boolean | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          email_notifications?: boolean | null
          employee_updates?: boolean | null
          fuel_level_warnings?: boolean | null
          id?: string
          production_alerts?: boolean | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notification_settings_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      pallet_movements: {
        Row: {
          client_name: string
          comments: string | null
          created_at: string | null
          delivery_date: string
          delivery_note: string
          destination: string
          id: string
          pallets_loaded: number
          product_type: string
          status: string
          vehicle_registration: string
        }
        Insert: {
          client_name: string
          comments?: string | null
          created_at?: string | null
          delivery_date: string
          delivery_note: string
          destination: string
          id?: string
          pallets_loaded: number
          product_type: string
          status?: string
          vehicle_registration: string
        }
        Update: {
          client_name?: string
          comments?: string | null
          created_at?: string | null
          delivery_date?: string
          delivery_note?: string
          destination?: string
          id?: string
          pallets_loaded?: number
          product_type?: string
          status?: string
          vehicle_registration?: string
        }
        Relationships: []
      }
      pallet_returns: {
        Row: {
          comments: string | null
          condition: string
          created_at: string | null
          id: string
          pallet_movement_id: string
          pallets_returned: number
          return_date: string
        }
        Insert: {
          comments?: string | null
          condition: string
          created_at?: string | null
          id?: string
          pallet_movement_id: string
          pallets_returned: number
          return_date: string
        }
        Update: {
          comments?: string | null
          condition?: string
          created_at?: string | null
          id?: string
          pallet_movement_id?: string
          pallets_returned?: number
          return_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "pallet_returns_pallet_movement_id_fkey"
            columns: ["pallet_movement_id"]
            isOneToOne: false
            referencedRelation: "pallet_movements"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          amount: number
          created_at: string
          employee_id: number
          id: string
          notes: string | null
          payment_date: string
          payment_type: Database["public"]["Enums"]["payment_type"]
          status: Database["public"]["Enums"]["payment_status"]
        }
        Insert: {
          amount: number
          created_at?: string
          employee_id: number
          id?: string
          notes?: string | null
          payment_date: string
          payment_type: Database["public"]["Enums"]["payment_type"]
          status?: Database["public"]["Enums"]["payment_status"]
        }
        Update: {
          amount?: number
          created_at?: string
          employee_id?: number
          id?: string
          notes?: string | null
          payment_date?: string
          payment_type?: Database["public"]["Enums"]["payment_type"]
          status?: Database["public"]["Enums"]["payment_status"]
        }
        Relationships: [
          {
            foreignKeyName: "payments_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      production_entries: {
        Row: {
          brick_type_id: string
          created_at: string
          date: string
          id: number
          pallet_count: number
        }
        Insert: {
          brick_type_id: string
          created_at?: string
          date: string
          id?: number
          pallet_count: number
        }
        Update: {
          brick_type_id?: string
          created_at?: string
          date?: string
          id?: number
          pallet_count?: number
        }
        Relationships: [
          {
            foreignKeyName: "production_entries_brick_type_id_fkey"
            columns: ["brick_type_id"]
            isOneToOne: false
            referencedRelation: "management_brick_types"
            referencedColumns: ["id"]
          },
        ]
      }
      production_targets: {
        Row: {
          brick_type_id: string
          created_at: string
          created_by: string | null
          id: string
          period_type: string
          target_bricks: number
          target_date: string
          target_pallets: number
          updated_at: string
        }
        Insert: {
          brick_type_id: string
          created_at?: string
          created_by?: string | null
          id?: string
          period_type?: string
          target_bricks?: number
          target_date: string
          target_pallets?: number
          updated_at?: string
        }
        Update: {
          brick_type_id?: string
          created_at?: string
          created_by?: string | null
          id?: string
          period_type?: string
          target_bricks?: number
          target_date?: string
          target_pallets?: number
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          active: boolean | null
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string | null
          username: string | null
        }
        Insert: {
          active?: boolean | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id: string
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          username?: string | null
        }
        Update: {
          active?: boolean | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string | null
          username?: string | null
        }
        Relationships: []
      }
      setting_production_entries: {
        Row: {
          brick_type_id: string
          chamber_number: number | null
          created_at: string
          date: string
          fire_id: string
          hour: number | null
          id: number
          is_night_shift: boolean | null
          is_overtime: boolean | null
          pallet_count: number
          team_id: string
        }
        Insert: {
          brick_type_id: string
          chamber_number?: number | null
          created_at?: string
          date?: string
          fire_id: string
          hour?: number | null
          id?: number
          is_night_shift?: boolean | null
          is_overtime?: boolean | null
          pallet_count: number
          team_id: string
        }
        Update: {
          brick_type_id?: string
          chamber_number?: number | null
          created_at?: string
          date?: string
          fire_id?: string
          hour?: number | null
          id?: number
          is_night_shift?: boolean | null
          is_overtime?: boolean | null
          pallet_count?: number
          team_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "setting_production_entries_brick_type_id_fkey"
            columns: ["brick_type_id"]
            isOneToOne: false
            referencedRelation: "management_brick_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "setting_production_entries_fire_id_fkey"
            columns: ["fire_id"]
            isOneToOne: false
            referencedRelation: "fires"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "setting_production_entries_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      spiral_loads: {
        Row: {
          carbon: number
          created_at: string
          date: string
          dnote_no: string
          dry_weight: number
          id: number
          mine: string
          supplier: string
          user_id: string
          wet_weight: number
        }
        Insert: {
          carbon: number
          created_at?: string
          date: string
          dnote_no: string
          dry_weight: number
          id?: number
          mine: string
          supplier: string
          user_id: string
          wet_weight: number
        }
        Update: {
          carbon?: number
          created_at?: string
          date?: string
          dnote_no?: string
          dry_weight?: number
          id?: number
          mine?: string
          supplier?: string
          user_id?: string
          wet_weight?: number
        }
        Relationships: [
          {
            foreignKeyName: "spiral_loads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      stock_levels: {
        Row: {
          brick_type_id: string
          created_at: string
          current_stock: number
          id: string
          last_updated: string
          location: string
        }
        Insert: {
          brick_type_id: string
          created_at?: string
          current_stock?: number
          id?: string
          last_updated?: string
          location?: string
        }
        Update: {
          brick_type_id?: string
          created_at?: string
          current_stock?: number
          id?: string
          last_updated?: string
          location?: string
        }
        Relationships: []
      }
      stock_movement_inbound: {
        Row: {
          brick_type: string
          created_at: string
          date: string
          dnote: string
          id: string
          notes: string | null
          quantity: number
          received: boolean | null
          received_at: string | null
          received_by: string | null
          transporter: string
          updated_at: string
          user_id: string
        }
        Insert: {
          brick_type: string
          created_at?: string
          date: string
          dnote: string
          id?: string
          notes?: string | null
          quantity: number
          received?: boolean | null
          received_at?: string | null
          received_by?: string | null
          transporter: string
          updated_at?: string
          user_id: string
        }
        Update: {
          brick_type?: string
          created_at?: string
          date?: string
          dnote?: string
          id?: string
          notes?: string | null
          quantity?: number
          received?: boolean | null
          received_at?: string | null
          received_by?: string | null
          transporter?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      stock_movement_outbound: {
        Row: {
          brick_type: string
          client_name: string
          created_at: string
          delivery_note: string
          id: string
          notes: string | null
          quantity: number
          sold_date: string
          updated_at: string
          user_id: string
        }
        Insert: {
          brick_type: string
          client_name: string
          created_at?: string
          delivery_note: string
          id?: string
          notes?: string | null
          quantity: number
          sold_date: string
          updated_at?: string
          user_id: string
        }
        Update: {
          brick_type?: string
          client_name?: string
          created_at?: string
          delivery_note?: string
          id?: string
          notes?: string | null
          quantity?: number
          sold_date?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      strapping_records: {
        Row: {
          created_at: string
          date: string
          employee_id: number
          id: string
          pallet_count: number
          rate_per_pallet: number
          team_id: string | null
          total_pay: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          date: string
          employee_id: number
          id?: string
          pallet_count: number
          rate_per_pallet?: number
          team_id?: string | null
          total_pay: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          date?: string
          employee_id?: number
          id?: string
          pallet_count?: number
          rate_per_pallet?: number
          team_id?: string | null
          total_pay?: number
          updated_at?: string
        }
        Relationships: []
      }
      system_config: {
        Row: {
          auto_backup: boolean | null
          company_name: string | null
          created_at: string | null
          currency: string | null
          dashboard_layout: string | null
          id: string
          language: string | null
          logo_url: string | null
          theme: string | null
          timezone: string | null
          updated_at: string | null
        }
        Insert: {
          auto_backup?: boolean | null
          company_name?: string | null
          created_at?: string | null
          currency?: string | null
          dashboard_layout?: string | null
          id?: string
          language?: string | null
          logo_url?: string | null
          theme?: string | null
          timezone?: string | null
          updated_at?: string | null
        }
        Update: {
          auto_backup?: boolean | null
          company_name?: string | null
          created_at?: string | null
          currency?: string | null
          dashboard_layout?: string | null
          id?: string
          language?: string | null
          logo_url?: string | null
          theme?: string | null
          timezone?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      team_memberships: {
        Row: {
          created_at: string
          employee_id: number
          id: string
          team_id: string
        }
        Insert: {
          created_at?: string
          employee_id: number
          id?: string
          team_id: string
        }
        Update: {
          created_at?: string
          employee_id?: number
          id?: string
          team_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_memberships_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_memberships_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      teams: {
        Row: {
          id: string
          name: string
        }
        Insert: {
          id: string
          name: string
        }
        Update: {
          id?: string
          name?: string
        }
        Relationships: []
      }
      user_sessions: {
        Row: {
          created_at: string
          expires_at: string
          id: string
          ip_address: unknown | null
          last_accessed: string
          session_token: string
          user_agent: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          expires_at: string
          id?: string
          ip_address?: unknown | null
          last_accessed?: string
          session_token: string
          user_agent?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          expires_at?: string
          id?: string
          ip_address?: unknown | null
          last_accessed?: string
          session_token?: string
          user_agent?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          active: boolean | null
          created_at: string | null
          email: string | null
          full_name: string
          id: string
          password_hash: string
          role: Database["public"]["Enums"]["user_role"] | null
          updated_at: string | null
          username: string
        }
        Insert: {
          active?: boolean | null
          created_at?: string | null
          email?: string | null
          full_name: string
          id?: string
          password_hash: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
          username: string
        }
        Update: {
          active?: boolean | null
          created_at?: string | null
          email?: string | null
          full_name?: string
          id?: string
          password_hash?: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
          username?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_current_user_context: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          user_role: Database["public"]["Enums"]["user_role"]
        }[]
      }
      get_current_user_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: Database["public"]["Enums"]["user_role"]
      }
      is_authenticated_user: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_current_user_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      set_config: {
        Args: {
          setting_name: string
          setting_value: string
          is_local?: boolean
        }
        Returns: undefined
      }
      update_chamber_fire_status: {
        Args:
          | {
              p_kiln_id: string
              p_chamber_number: number
              p_fire_id: string
              p_is_burning: boolean
              p_user_id?: string
            }
          | {
              p_kiln_id: string
              p_chamber_number: number
              p_is_burning: boolean
              p_user_id?: string
            }
        Returns: undefined
      }
    }
    Enums: {
      fire_status: "active" | "inactive" | "maintenance"
      kiln_status: "operational" | "offline" | "maintenance"
      payment_status: "Paid" | "Pending" | "Processing" | "Failed"
      payment_type:
        | "Salary"
        | "Bonus"
        | "Overtime"
        | "Dehacking"
        | "Setting"
        | "Adjustment"
      user_role:
        | "admin"
        | "manager"
        | "finance"
        | "factory_supervisor"
        | "yard_supervisor"
        | "office_admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      fire_status: ["active", "inactive", "maintenance"],
      kiln_status: ["operational", "offline", "maintenance"],
      payment_status: ["Paid", "Pending", "Processing", "Failed"],
      payment_type: [
        "Salary",
        "Bonus",
        "Overtime",
        "Dehacking",
        "Setting",
        "Adjustment",
      ],
      user_role: [
        "admin",
        "manager",
        "finance",
        "factory_supervisor",
        "yard_supervisor",
        "office_admin",
      ],
    },
  },
} as const
