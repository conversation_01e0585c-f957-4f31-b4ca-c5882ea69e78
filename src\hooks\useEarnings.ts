
import { useQuery } from '@tanstack/react-query';
import { calculateAllPendingEarnings, PendingEarnings } from '@/data/earningsCalculations';
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

export function usePendingEarnings() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery<PendingEarnings[], Error>({
    queryKey: ['pendingEarnings'],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return calculateAllPendingEarnings(userId || undefined);
    },
  });
}
