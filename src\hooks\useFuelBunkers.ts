
import { useQuery } from "@tanstack/react-query";
import { getFuelBunkers } from "@/data/fuelBunkersData";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

export function useFuelBunkers() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ['fuelBunkers'],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getFuelBunkers(userId || undefined);
    },
    // Removed 'onError' property to comply with Tanstack Query v5 API
  });
}
