-- Fix RLS policies for users table to use custom authentication system
-- This migration updates the users table policies to use current_setting('app.current_user_id', true)
-- instead of auth.uid() since the app uses custom authentication

-- Drop existing policies for users table
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Administrators can view all users" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Administrators can manage all users" ON public.users;

-- Update the get_current_user_role function to use custom authentication
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS user_role
LANGUAGE plpgsql
STABLE SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  current_role user_role;
  context_user_id text;
BEGIN
  -- Get user ID from session context
  context_user_id := current_setting('app.current_user_id', true);
  
  IF context_user_id IS NULL OR context_user_id = '' THEN
    RETURN NULL;
  END IF;
  
  SELECT role INTO current_role
  FROM public.users 
  WHERE id = context_user_id::uuid AND active = true;
  
  RETURN current_role;
END;
$$;

-- Create new RLS policies for users table using custom authentication
CREATE POLICY "Users can view their own profile" 
  ON public.users 
  FOR SELECT 
  USING (id::text = current_setting('app.current_user_id', true));

CREATE POLICY "Administrators can view all users" 
  ON public.users 
  FOR SELECT 
  USING (public.get_current_user_role() = 'admin');

CREATE POLICY "Users can update their own profile" 
  ON public.users 
  FOR UPDATE 
  USING (id::text = current_setting('app.current_user_id', true));

CREATE POLICY "Administrators can insert users" 
  ON public.users 
  FOR INSERT 
  WITH CHECK (public.get_current_user_role() = 'admin');

CREATE POLICY "Administrators can update all users" 
  ON public.users 
  FOR UPDATE 
  USING (public.get_current_user_role() = 'admin');

CREATE POLICY "Administrators can delete users" 
  ON public.users 
  FOR DELETE 
  USING (public.get_current_user_role() = 'admin');

-- Fix notification_settings policies
DROP POLICY IF EXISTS "Users can manage their own notification settings" ON public.notification_settings;

CREATE POLICY "Users can manage their own notification settings" 
  ON public.notification_settings 
  FOR ALL 
  USING (user_id::text = current_setting('app.current_user_id', true));

-- Fix system_config policies  
DROP POLICY IF EXISTS "Administrators can manage system config" ON public.system_config;
DROP POLICY IF EXISTS "All users can view system config" ON public.system_config;

CREATE POLICY "Administrators can manage system config" 
  ON public.system_config 
  FOR ALL 
  USING (public.get_current_user_role() = 'admin');

CREATE POLICY "All authenticated users can view system config" 
  ON public.system_config 
  FOR SELECT 
  USING (current_setting('app.current_user_id', true) IS NOT NULL AND current_setting('app.current_user_id', true) != '');
