
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getPayments, addPayment, PaymentWithEmployeeName, NewPayment } from "@/data/paymentsData";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { useToast } from "@/hooks/use-toast";

export function usePayments() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery<PaymentWithEmployeeName[], Error>({
    queryKey: ['payments'],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getPayments(userId || undefined);
    },
  });
}

export function useAddPayment() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: (payment: NewPayment | NewPayment[]) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return addPayment(payment, userId || undefined);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['pendingEarnings'] });
      toast({ title: "Success", description: "Payment added successfully." });
    },
    onError: (error: Error) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });
}
