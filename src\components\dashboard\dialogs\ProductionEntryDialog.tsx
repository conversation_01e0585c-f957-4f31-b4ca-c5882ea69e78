import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar, Clock, User } from "lucide-react";
import { toast } from "sonner";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { useActivityTracking } from "@/hooks/useActivityTracking";
import { getExtrudedBrickTypes, type ManagementBrickType } from "@/data/managementBrickTypes";
import { addProductionEntry } from "@/data/productionStore";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

interface ProductionEntryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const ProductionEntryDialog = ({ isOpen, onClose, onSuccess }: ProductionEntryDialogProps) => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const { logActivity } = useActivityTracking();
  const queryClient = useQueryClient();
  const [brickTypeId, setBrickTypeId] = useState<string | undefined>(undefined);
  const [palletCount, setPalletCount] = useState("");
  const [date, setDate] = useState(() => new Date().toISOString().slice(0, 10));
  const [clockIn, setClockIn] = useState("");
  const [clockOut, setClockOut] = useState("");

  const { data: brickTypesData = [], isLoading: isLoadingBrickTypes } = useQuery<ManagementBrickType[]>({
    queryKey: ['extrudedBrickTypes'],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getExtrudedBrickTypes(userId || undefined);
    },
  });

  const brickType = brickTypesData.find(bt => bt.id === brickTypeId);
  const parsedPallets = parseInt(palletCount, 10) || 0;
  const totalBricks = brickType ? brickType.bricks_per_pallet * parsedPallets : 0;

  const handleSaveProduction = async () => {
    if (!brickTypeId) {
      toast.error("Please select a brick type.");
      return;
    }
    if (!palletCount || parsedPallets <= 0) {
      toast.error("Enter a valid pallet count.");
      return;
    }

    try {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);

      await addProductionEntry({
        date,
        brick_type_id: brickTypeId,
        pallet_count: parsedPallets,
      }, userId || undefined);

      // Log the activity
      const brickTypeName = brickType?.name || 'Unknown';
      logActivity(
        'Factory Production Entry',
        `Recorded ${parsedPallets} pallets of ${brickTypeName} (${totalBricks} bricks total)`,
        'production'
      );

      // Invalidate queries to refresh dashboard data
      queryClient.invalidateQueries({ queryKey: ['dashboardMetrics'] });
      queryClient.invalidateQueries({ queryKey: ['productionEntries'] });
      queryClient.invalidateQueries({ queryKey: ['productionForLoss'] });
      queryClient.invalidateQueries({ queryKey: ['analyticsData'] });

      toast.success("Production recorded!");
      onSuccess();
      handleClose();
    } catch (error) {
      toast.error("Failed to save production. Please try again.");
      console.error(error);
    }
  };

  const handleClose = () => {
    setBrickTypeId(undefined);
    setPalletCount("");
    setDate(new Date().toISOString().slice(0, 10));
    setClockIn("");
    setClockOut("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto p-4 sm:p-6 bg-white rounded-lg mx-4">
        <DialogHeader className="mb-2">
          <DialogTitle className="text-lg sm:text-xl font-semibold">Factory Output Management</DialogTitle>
          <DialogDescription className="text-sm sm:text-base">
            Record extruded brick production
          </DialogDescription>
          {/* Show logged-in supervisor */}
          <div className="flex items-center gap-2 mt-2 p-2 bg-blue-50 rounded-lg">
            <User size={16} className="text-blue-600 flex-shrink-0" />
            <span className="text-sm text-blue-800 min-w-0">
              Supervisor: <strong className="truncate">{currentUser?.full_name}</strong>
            </span>
          </div>
        </DialogHeader>
        <form
          onSubmit={async (e) => {
            e.preventDefault();
            await handleSaveProduction();
          }}
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 pb-2">
            <div className="flex flex-col gap-1">
              <Label htmlFor="production-date" className="font-medium">Production Date</Label>
              <div className="relative">
                <Input
                  type="date"
                  id="production-date"
                  value={date}
                  onChange={e => setDate(e.target.value)}
                  className="pr-10"
                  required
                />
                <Calendar className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
              </div>
            </div>
            <div className="flex flex-col gap-1">
              <Label htmlFor="clock-in" className="font-medium">Clock-In Time</Label>
              <div className="relative">
                <Input
                  type="time"
                  id="clock-in"
                  value={clockIn}
                  onChange={e => setClockIn(e.target.value)}
                  className="pr-10"
                />
                <Clock className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
              </div>
            </div>
            <div className="flex flex-col gap-1">
              <Label htmlFor="clock-out" className="font-medium">Clock-Out Time</Label>
              <div className="relative">
                <Input
                  type="time"
                  id="clock-out"
                  value={clockOut}
                  onChange={e => setClockOut(e.target.value)}
                  className="pr-10"
                />
                <Clock className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
              </div>
            </div>
            <div className="flex flex-col gap-1">
              <Label htmlFor="extruded-brick" className="font-medium">Extruded Bricks</Label>
              <Select value={brickTypeId || ""} onValueChange={setBrickTypeId}>
                <SelectTrigger
                  id="extruded-brick"
                  className="w-full"
                >
                  <SelectValue placeholder="Select brick type" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingBrickTypes && (
                    <SelectItem value="" disabled>Loading...</SelectItem>
                  )}
                  {brickTypesData.map(bt => (
                    <SelectItem key={bt.id} value={bt.id}>
                      {bt.name} <span className="text-xs text-gray-500">({bt.bricks_per_pallet} per pallet)</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-1 sm:col-span-2">
              <Label htmlFor="pallet-count" className="font-medium">Pallet Count</Label>
              <Input
                id="pallet-count"
                type="number"
                min={1}
                placeholder="Enter pallet count"
                value={palletCount}
                onChange={e => setPalletCount(e.target.value.replace(/[^0-9]/g, ""))}
                className="text-base" // Prevent zoom on iOS
                required
              />
            </div>
          </div>
          {brickType && parsedPallets > 0 && (
            <div className="bg-gray-50 border p-2 rounded text-sm text-gray-700 my-2">
              <strong>Total bricks:</strong> {totalBricks.toLocaleString()} ({parsedPallets} pallet{parsedPallets !== 1 ? "s" : ""} × {brickType.bricks_per_pallet})
            </div>
          )}
          <DialogFooter className="mt-4 flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              type="button"
              onClick={handleClose}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-orange-500 hover:bg-orange-600 w-full sm:w-auto order-1 sm:order-2 min-h-[44px]"
            >
              Save Production
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
