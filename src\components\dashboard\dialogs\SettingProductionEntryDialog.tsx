import { useState } from "react";
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Calendar as CalendarIcon, User } from "lucide-react";
import { toast } from "sonner";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { useActivityTracking } from "@/hooks/useActivityTracking";
import { getExtrudedBrickTypes, type ManagementBrickType } from "@/data/managementBrickTypes";
import { addSettingProductionEntry } from "@/data/settingProductionStore";
import { getTeams, type Team } from "@/data/fuelBunkersData";
import { getFires, type Fire } from "@/data/fireData";
import { getKilns, type KilnConfig } from "@/data/kilnData";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";

interface SettingProductionEntryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const SettingProductionEntryDialog = ({
  isOpen,
  onClose,
  onSuccess
}: SettingProductionEntryDialogProps) => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const { logActivity } = useActivityTracking();
  const queryClient = useQueryClient();
  const [teamId, setTeamId] = useState<string | undefined>(undefined);
  const [fireId, setFireId] = useState<string | undefined>(undefined);
  const [chamberNumber, setChamberNumber] = useState<string | undefined>(undefined);
  const [brickTypeId, setBrickTypeId] = useState<string | undefined>(undefined);
  const [palletCount, setPalletCount] = useState("");
  const [hour, setHour] = useState<string | undefined>(undefined);
  const [date, setDate] = useState(() => new Date().toISOString().slice(0, 10));
  const [isOvertime, setIsOvertime] = useState(false);
  const [isNightShift, setIsNightShift] = useState(false);

  const { data: teamsData = [], isLoading: isLoadingTeams } = useQuery<Team[]>({
    queryKey: ['teams'],
    queryFn: () => getTeams()
  });

  const { data: firesData = [], isLoading: isLoadingFires } = useQuery<Fire[]>({
    queryKey: ['fires'],
    queryFn: () => getFires()
  });

  const { data: kilnsData = [], isLoading: isLoadingKilns } = useQuery<KilnConfig[]>({
    queryKey: ['kilns'],
    queryFn: () => getKilns()
  });

  const { data: brickTypesData = [], isLoading: isLoadingBrickTypes } = useQuery<ManagementBrickType[]>({
    queryKey: ['extrudedBrickTypes'],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getExtrudedBrickTypes(userId || undefined);
    }
  });

  // Get the selected fire to determine available chambers
  const selectedFire = firesData.find(f => f.id === fireId);
  const selectedKiln = kilnsData.find(k => k.id === selectedFire?.kiln_id);

  // Generate chamber numbers based on kiln type: Habla gets 12, Kilns 1-5 get 24
  const chambers = Array.from({
    length: selectedKiln?.name === "Habla" ? 12 : 24
  }, (_, i) => i + 1);

  const handleSaveProduction = async () => {
    if (!teamId) {
      toast.error("Please select a team.");
      return;
    }
    if (!fireId) {
      toast.error("Please select a fire/chamber.");
      return;
    }
    if (!chamberNumber) {
      toast.error("Please select a chamber number.");
      return;
    }
    if (!brickTypeId) {
      toast.error("Please select a brick type.");
      return;
    }
    if (!hour) {
      toast.error("Please select an hour.");
      return;
    }
    const parsedPallets = parseInt(palletCount, 10) || 0;
    if (!palletCount || parsedPallets <= 0) {
      toast.error("Enter a valid pallet count.");
      return;
    }
    try {
      // Convert hour string (e.g., "14:00") to just the hour number (e.g., 14)
      const hourNumber = parseInt(hour.split(':')[0]);
      console.log('[SettingProductionEntry] Attempting to save:', {
        date,
        teamId,
        fireId,
        chamberNumber: parseInt(chamberNumber),
        brickTypeId,
        palletCount: parsedPallets,
        hour: hourNumber,
        isOvertime,
        isNightShift,
        currentUser: currentUser?.username
      });
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);

      await addSettingProductionEntry({
        date,
        teamId,
        fireId,
        chamberNumber: parseInt(chamberNumber),
        brickTypeId: brickTypeId,
        palletCount: parsedPallets,
        hour: hourNumber,
        isOvertime,
        isNightShift
      }, userId || undefined);

      // Log the activity
      const teamName = teamsData.find(t => t.id === teamId)?.name || 'Unknown Team';
      const fireName = firesData.find(f => f.id === fireId)?.name || 'Unknown Fire';
      const brickType = brickTypesData.find(bt => bt.id === brickTypeId);
      const brickTypeName = brickType?.name || 'Unknown';
      const kilnName = selectedKiln?.name || 'Unknown Kiln';
      const totalBricks = brickType ? brickType.bricks_per_pallet * parsedPallets : 0;
      const shiftInfo = isNightShift ? ' (Night Shift)' : ' (Day Shift)';
      const overtimeInfo = isOvertime ? ' (Overtime)' : '';
      logActivity('Setting Team Production Entry', `Recorded ${parsedPallets} pallets of ${brickTypeName} for ${teamName} at ${fireName} Chamber ${chamberNumber} in ${kilnName} (${totalBricks} bricks total)${shiftInfo}${overtimeInfo}`, 'production');

      // Invalidate queries to refresh dashboard data
      queryClient.invalidateQueries({ queryKey: ['dashboardMetrics'] });
      queryClient.invalidateQueries({ queryKey: ['settingProductionForLoss'] });
      queryClient.invalidateQueries({ queryKey: ['analyticsData'] });
      queryClient.invalidateQueries({ queryKey: ['settingTeamsCardSummary'] });
      console.log('[SettingProductionEntry] Successfully saved production entry');
      toast.success("Setting production recorded!");
      onSuccess();
      handleClose();
    } catch (error) {
      console.error('[SettingProductionEntry] Error saving production:', error);
      console.error('[SettingProductionEntry] Error details:', JSON.stringify(error, null, 2));
      toast.error(`Failed to save production: ${error.message || 'Unknown error'}`);
    }
  };

  const handleClose = () => {
    setTeamId(undefined);
    setFireId(undefined);
    setChamberNumber(undefined);
    setBrickTypeId(undefined);
    setPalletCount("");
    setHour(undefined);
    setDate(new Date().toISOString().slice(0, 10));
    setIsOvertime(false);
    setIsNightShift(false);
    onClose();
  };

  // Calculate derived values for display
  const brickType = brickTypesData.find(bt => bt.id === brickTypeId);
  const parsedPallets = parseInt(palletCount, 10) || 0;
  const totalBricks = brickType ? brickType.bricks_per_pallet * parsedPallets : 0;

  // Generate hours array (0-23)
  const hours = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, '0');
    return `${hour}:00`;
  });

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && handleClose()}>
      <DialogContent className="sm:max-w-lg p-6 bg-white rounded-lg">
        <DialogHeader className="mb-2">
          <DialogTitle className="text-xl font-semibold">Setting Team Production</DialogTitle>
          <DialogDescription>
            Record production for a setting team.
          </DialogDescription>
          {/* Show logged-in supervisor */}
          <div className="flex items-center gap-2 mt-2 p-2 bg-green-50 rounded-lg">
            <User size={16} className="text-green-600" />
            <span className="text-sm text-green-800">
              Supervisor: <strong>{currentUser?.full_name}</strong>
            </span>
          </div>
        </DialogHeader>
        <form onSubmit={async e => {
          e.preventDefault();
          await handleSaveProduction();
        }}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pb-2">
            <div className="flex flex-col gap-1">
              <Label htmlFor="production-date" className="font-medium">Production Date</Label>
              <div className="relative">
                <Input type="date" id="production-date" value={date} onChange={e => setDate(e.target.value)} className="pr-10" required />
                <CalendarIcon className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
              </div>
            </div>

            <div className="flex flex-col gap-1">
              <Label htmlFor="team-select" className="font-medium">Team</Label>
              <Select value={teamId || ""} onValueChange={setTeamId}>
                <SelectTrigger id="team-select" className="w-full">
                  <SelectValue placeholder="Select team" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingTeams && <SelectItem value="" disabled>Loading...</SelectItem>}
                  {teamsData.map(team => <SelectItem key={team.id} value={team.id}>{team.name}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col gap-1">
              <Label htmlFor="fire-select" className="font-medium">Fire</Label>
              <Select value={fireId || ""} onValueChange={value => {
                setFireId(value);
                setChamberNumber(undefined); // Reset chamber when fire changes
              }}>
                <SelectTrigger id="fire-select" className="w-full">
                  <SelectValue placeholder="Select fire/chamber" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingFires && <SelectItem value="" disabled>Loading...</SelectItem>}
                  {firesData.map(fire => <SelectItem key={fire.id} value={fire.id}>{fire.name}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col gap-1">
              <Label htmlFor="chamber-select" className="font-medium">Chamber</Label>
              <Select value={chamberNumber || ""} onValueChange={setChamberNumber} disabled={!fireId}>
                <SelectTrigger id="chamber-select" className="w-full">
                  <SelectValue placeholder="Select chamber" />
                </SelectTrigger>
                <SelectContent>
                  {chambers.map(chamber => <SelectItem key={chamber} value={chamber.toString()}>
                    Chamber {chamber}
                  </SelectItem>)}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex flex-col gap-1">
              <Label htmlFor="extruded-brick" className="font-medium">Extruded Bricks</Label>
              <Select value={brickTypeId || ""} onValueChange={setBrickTypeId}>
                <SelectTrigger id="extruded-brick" className="w-full">
                  <SelectValue placeholder="Select brick type" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingBrickTypes && <SelectItem value="" disabled>Loading...</SelectItem>}
                  {brickTypesData.map(bt => <SelectItem key={bt.id} value={bt.id}>
                    {bt.name} <span className="text-xs text-gray-500">({bt.bricks_per_pallet} per pallet)</span>
                  </SelectItem>)}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col gap-1">
              <Label htmlFor="hour-select" className="font-medium">Hour</Label>
              <Select value={hour || ""} onValueChange={setHour}>
                <SelectTrigger id="hour-select" className="w-full">
                  <SelectValue placeholder="Select hour" />
                </SelectTrigger>
                <SelectContent>
                  {hours.map(h => <SelectItem key={h} value={h}>{h}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col gap-1 md:col-span-2">
              <Label htmlFor="pallet-count" className="font-medium">Pallet Count</Label>
              <Input id="pallet-count" type="number" min={1} placeholder="Enter pallet count" value={palletCount} onChange={e => setPalletCount(e.target.value.replace(/[^0-9]/g, ""))} required />
            </div>

            {/* Overtime and Night Shift Radio Buttons */}
            <div className="flex flex-col gap-2">
              <Label className="font-medium">Overtime</Label>
              <RadioGroup value={isOvertime ? "yes" : "no"} onValueChange={value => setIsOvertime(value === "yes")} className="flex flex-row gap-4">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="overtime-no" />
                  <Label htmlFor="overtime-no" className="text-sm">No</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="overtime-yes" />
                  <Label htmlFor="overtime-yes" className="text-sm">Yes</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="flex flex-col gap-2">
              <Label className="font-medium">Night Shift</Label>
              <RadioGroup value={isNightShift ? "yes" : "no"} onValueChange={value => setIsNightShift(value === "yes")} className="flex flex-row gap-4">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="nightshift-no" />
                  <Label htmlFor="nightshift-no" className="text-sm">No</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="nightshift-yes" />
                  <Label htmlFor="nightshift-yes" className="text-sm">Yes</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          {brickType && parsedPallets > 0 && (
            <div className="bg-gray-50 border p-2 rounded text-sm text-gray-700 my-2">
              <strong>Total bricks:</strong> {totalBricks.toLocaleString()} ({parsedPallets} pallet{parsedPallets !== 1 ? "s" : ""} × {brickType.bricks_per_pallet})
              {selectedKiln && chamberNumber && (
                <div className="mt-1">
                  <strong>Kiln:</strong> {selectedKiln.name}, <strong>Chamber:</strong> {chamberNumber}
                </div>
              )}
              {(isOvertime || isNightShift) && (
                <div className="mt-1 text-blue-600">
                  {isNightShift && <span className="mr-2">🌙 Night Shift</span>}
                  {isOvertime && <span>⏰ Overtime</span>}
                </div>
              )}
            </div>
          )}
          <DialogFooter className="mt-4">
            <Button variant="outline" type="button" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-purple-500 hover:bg-purple-600">
              Save Production
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};