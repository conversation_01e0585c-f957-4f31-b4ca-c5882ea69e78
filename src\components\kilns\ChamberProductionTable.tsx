import React, { use<PERSON>emo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Loader2, Calculator } from "lucide-react";
import { format } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { getKilns } from "@/data/kilnData";
import { useSettingProductionEntries } from "@/hooks/useProductionEntries";
import { supabase } from "@/integrations/supabase/client";

interface ChamberProductionTableProps {
  selectedDate: string;
}

interface SettingProductionEntry {
  id: number;
  brick_type_id: string;
  pallet_count: number;
  fire_id?: string;
  chamber_number?: number;
  date: string;
  management_brick_types?: {
    name: string;
    bricks_per_pallet: number;
  };
  fires?: {
    kiln_id: string;
  };
}

interface DehackingEntry {
  id: number;
  brick_type_id: string;
  pallet_count: number;
  fire_id?: string;
  chamber_number?: number;
  date: string;
  management_brick_types?: {
    name: string;
    bricks_per_pallet: number;
  };
  fires?: {
    kiln_id: string;
  };
}

// Helper function to get chamber count for each kiln
const getChamberCount = (kilnName: string): number => {
  if (kilnName === 'Habla') {
    return 10;
  }
  return 24; // Kiln 1-5 should have 24 chambers each
};

export const ChamberProductionTable: React.FC<ChamberProductionTableProps> = ({
  selectedDate,
}) => {
  // Fetch kilns data
  const { data: kilns = [], isLoading: kilnsLoading } = useQuery({
    queryKey: ['kilns'],
    queryFn: () => getKilns(),
    select: (data) => {
      // Sort kilns: Habla first, then Kiln 1-5
      return data.sort((a, b) => {
        if (a.name === 'Habla') return -1;
        if (b.name === 'Habla') return 1;

        const aNum = parseInt(a.name.replace('Kiln ', ''));
        const bNum = parseInt(b.name.replace('Kiln ', ''));
        return aNum - bNum;
      });
    }
  });

  // Use the setting production entries hook with proper user context
  const { data: allSettingEntries = [], isLoading: settingEntriesLoading } = useSettingProductionEntries();

  // Filter by selected date
  const settingEntries = useMemo(() => {
    return allSettingEntries.filter(entry => entry.date === selectedDate);
  }, [allSettingEntries, selectedDate]);

  // Fetch dehacking entries
  const { data: dehackingEntries = [], isLoading: dehackingEntriesLoading } = useQuery({
    queryKey: ['dehacking-entries', selectedDate],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('dehacking_entries')
        .select('*, management_brick_types(name, bricks_per_pallet), fires(kiln_id)')
        .eq('date', selectedDate);

      if (error) {
        console.error("Error fetching dehacking entries:", error);
        return [];
      }

      return data || [];
    },
  });

  // Calculate chamber data for each kiln
  const calculateChamberData = (kiln: any) => {
    const chamberCount = getChamberCount(kiln.name);
    const chambers = [];

    for (let i = 1; i <= chamberCount; i++) {
      // Calculate setting data for this chamber
      const settingEntriesForChamber = settingEntries.filter(
        (entry: SettingProductionEntry) => entry.fires?.kiln_id === kiln.id && entry.chamber_number === i
      );

      const bricksSet = settingEntriesForChamber.reduce((sum, entry) => {
        // For now, use a default value since management_brick_types relation needs to be properly set up
        const bricksPerPallet = 320; // Default imperial pallet count
        return sum + (entry.pallet_count * bricksPerPallet);
      }, 0);

      // Calculate dehacking data for this chamber
      const dehackingEntriesForChamber = dehackingEntries.filter(
        (entry: DehackingEntry) => entry.fires?.kiln_id === kiln.id && entry.chamber_number === i
      );

      const bricksDehacked = dehackingEntriesForChamber.reduce((sum, entry) => {
        const bricksPerPallet = entry.management_brick_types?.bricks_per_pallet || 0;
        return sum + (entry.pallet_count * bricksPerPallet);
      }, 0);

      // Calculate output and breakage
      const output = bricksDehacked;
      const breakage = bricksSet - bricksDehacked;
      const breakagePercentage = bricksSet > 0 ? (breakage / bricksSet) * 100 : 0;

      chambers.push({
        chamber: i,
        bricksSet,
        bricksDehacked,
        output,
        breakage,
        breakagePercentage,
      });
    }

    return chambers;
  };

  // Calculate totals across all kilns
  const calculateTotals = () => {
    let totalBricksSet = 0;
    let totalBricksDehacked = 0;
    let totalOutput = 0;
    let totalBreakage = 0;

    kilns.forEach(kiln => {
      const chambers = calculateChamberData(kiln);
      chambers.forEach(chamber => {
        totalBricksSet += chamber.bricksSet;
        totalBricksDehacked += chamber.bricksDehacked;
        totalOutput += chamber.output;
        totalBreakage += chamber.breakage;
      });
    });

    const totalBreakagePercentage = totalBricksSet > 0 ? (totalBreakage / totalBricksSet) * 100 : 0;

    return {
      totalBricksSet,
      totalBricksDehacked,
      totalOutput,
      totalBreakage,
      totalBreakagePercentage,
    };
  };

  if (kilnsLoading || settingEntriesLoading || dehackingEntriesLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Chamber Production Tracking</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading chamber production data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totals = calculateTotals();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Chamber Production Tracking</CardTitle>
        <div className="text-sm text-slate-600">
          Date: {format(new Date(selectedDate), 'PPP')}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {kilns.map((kiln) => {
            const chambers = calculateChamberData(kiln);

            return (
              <div key={kiln.id}>
                <h3 className="font-semibold text-lg mb-3">{kiln.name}</h3>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Chamber</TableHead>
                        <TableHead className="text-center">Bricks Set</TableHead>
                        <TableHead className="text-center">Bricks Dehacked</TableHead>
                        <TableHead className="text-center">Output</TableHead>
                        <TableHead className="text-center">Breakage</TableHead>
                        <TableHead className="text-center">Breakage %</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {chambers.map((chamber) => (
                        <TableRow key={chamber.chamber}>
                          <TableCell className="font-medium">Chamber {chamber.chamber}</TableCell>
                          <TableCell className="text-center">
                            {chamber.bricksSet > 0 ? chamber.bricksSet.toLocaleString() : '-'}
                          </TableCell>
                          <TableCell className="text-center">
                            {chamber.bricksDehacked > 0 ? chamber.bricksDehacked.toLocaleString() : '-'}
                          </TableCell>
                          <TableCell className="text-center font-medium">
                            {chamber.output > 0 ? chamber.output.toLocaleString() : '-'}
                          </TableCell>
                          <TableCell className="text-center">
                            <span className={chamber.breakage > 0 ? 'text-red-600 font-medium' : chamber.breakage < 0 ? 'text-blue-600 font-medium' : 'text-green-600'}>
                              {chamber.breakage !== 0 ? chamber.breakage.toLocaleString() : '-'}
                            </span>
                          </TableCell>
                          <TableCell className="text-center">
                            <span className={chamber.breakagePercentage > 5 ? 'text-red-600 font-medium' : 'text-slate-600'}>
                              {chamber.bricksSet > 0 ? `${chamber.breakagePercentage.toFixed(1)}%` : '-'}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            );
          })}

          {/* Summary Section */}
          <div className="border-t pt-4">
            <h3 className="font-semibold text-lg mb-3 flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              Daily Summary
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {totals.totalBricksSet.toLocaleString()}
                </div>
                <div className="text-sm text-slate-600">Total Bricks Set</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {totals.totalBricksDehacked.toLocaleString()}
                </div>
                <div className="text-sm text-slate-600">Total Bricks Dehacked</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-slate-800">
                  {totals.totalOutput.toLocaleString()}
                </div>
                <div className="text-sm text-slate-600">Total Output</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${totals.totalBreakage > 0 ? 'text-red-600' : totals.totalBreakage < 0 ? 'text-blue-600' : 'text-green-600'}`}>
                  {totals.totalBreakage.toLocaleString()}
                </div>
                <div className="text-sm text-slate-600">Total Breakage</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${totals.totalBreakagePercentage > 5 ? 'text-red-600' : 'text-green-600'}`}>
                  {totals.totalBreakagePercentage.toFixed(1)}%
                </div>
                <div className="text-sm text-slate-600">Breakage Rate</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
