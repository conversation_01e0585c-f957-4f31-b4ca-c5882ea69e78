
import { useState, useEffect } from "react";
import { useUser } from "@/contexts/UserContext";
import { Sidebar } from "@/components/layout/Sidebar";
import { Header } from "@/components/layout/Header";
import { DashboardContent } from "@/components/dashboard/DashboardContent";
import { DehackingPage } from "@/components/pages/DehackingPage";
import { KilnsPage } from "@/components/pages/KilnsPage";
import { PalletTrackingPage } from "@/components/pages/PalletTrackingPage";
import { BrickTypesPage } from "@/components/pages/BrickTypesPage";
import { SettingsPage } from "@/components/pages/SettingsPage";
import { CarbonSpiralTrackerPage } from "@/components/pages/CarbonSpiralTrackerPage";
import { PeopleManagementPage } from "@/components/pages/PeopleManagementPage";
import { FinancialManagementPage } from "@/components/pages/FinancialManagementPage";
import { AssetManagementPage } from "@/components/pages/AssetManagementPage";
import { ProductionTrackingPage } from "@/components/pages/ProductionTrackingPage";
import { BreakdownsPage } from "@/components/pages/BreakdownsPage";
import { LoadPlanningPage } from "@/components/pages/LoadPlanningPage";
import StockYardPage from "@/components/pages/StockYardPage";
import { AIInsightsPage } from "@/components/pages/AIInsightsPage";

export type MenuItem =
  | "dashboard"
  | "dehacking"
  | "people-management"
  | "kilns"
  | "pallet-tracking"
  | "brick-types"
  | "financial-management"
  | "asset-management"
  | "production-tracking"
  | "carbon-spiral-tracker"
  | "breakdowns"
  | "load-planning"
  | "stock-yard"
  | "ai-insights"
  | "settings";

const Index = () => {
  console.log('Index component starting to render...');

  const [activeMenuItem, setActiveMenuItem] = useState<MenuItem>("dashboard");
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  try {
    // Try to get user context - if this fails, we'll catch it
    const { canAccessMenuItem } = useUser();
    console.log('UserContext loaded successfully');

    // Check hash for navigation from quick access cards
    useEffect(() => {
      const hash = window.location.hash.substring(1);
      if (hash === 'pallet-tracking' && canAccessMenuItem('pallet-tracking')) {
        setActiveMenuItem('pallet-tracking');
        window.location.hash = ''; // Clear the hash
      }
    }, [canAccessMenuItem]);

    // Redirect to dashboard if user tries to access unauthorized page
    useEffect(() => {
      if (!canAccessMenuItem(activeMenuItem)) {
        setActiveMenuItem("dashboard");
      }
    }, [activeMenuItem, canAccessMenuItem]);

    // Test content renderer - adding components one by one
    const renderContent = () => {
      console.log('Rendering content for:', activeMenuItem);

      try {
        switch (activeMenuItem) {
          case "dashboard":
            // Now try the real DashboardContent component
            console.log('Rendering real DashboardContent...');
            return <DashboardContent />;
          case "dehacking":
            return <DehackingPage />;
          case "people-management":
            return <PeopleManagementPage />;
          case "kilns":
            return <KilnsPage />;
          case "pallet-tracking":
            return <PalletTrackingPage />;
          case "brick-types":
            return <BrickTypesPage />;
          case "financial-management":
            return <FinancialManagementPage />;
          case "asset-management":
            return <AssetManagementPage />;
          case "production-tracking":
            return <ProductionTrackingPage />;
          case "carbon-spiral-tracker":
            return <CarbonSpiralTrackerPage />;
          case "breakdowns":
            return <BreakdownsPage />;
          case "load-planning":
            return <LoadPlanningPage />;
          case "stock-yard":
            return <StockYardPage />;
          case "ai-insights":
            return <AIInsightsPage />;
          case "settings":
            return <SettingsPage />;
          default:
            return <DashboardContent />;
        }
      } catch (error) {
        console.error('Error rendering content for', activeMenuItem, ':', error);
        return (
          <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
            <h2 className="text-red-800 font-semibold mb-2">Error Loading {activeMenuItem}</h2>
            <p className="text-red-600 mb-4">There was an error loading this section: {error.message}</p>
            <button
              onClick={() => setActiveMenuItem('dashboard')}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Return to Dashboard
            </button>
          </div>
        );
      }
    };

    return (
      <div className="min-h-screen bg-slate-100 flex w-full">
        {/* Test with real Sidebar component */}
        <Sidebar
          activeMenuItem={activeMenuItem}
          onMenuItemClick={setActiveMenuItem}
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        <div className="flex-1 flex flex-col min-w-0">
          {/* Try real Header component again - added missing methods to AuthContext */}
          <Header
            onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
          />

          <main className="flex-1 p-6 overflow-auto">
            {renderContent()}
          </main>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error rendering Index component:', error);
    return (
      <div className="min-h-screen bg-red-50 p-8">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold text-red-800 mb-4">Error Loading Dashboard</h1>
          <p className="text-red-600 mb-4">There was an error loading the dashboard: {error.message}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }
};

export default Index;
