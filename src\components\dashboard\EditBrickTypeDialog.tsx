import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

interface EditBrickTypeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  brickType: any;
  onSave: () => void;
}

export const EditBrickTypeDialog = ({ open, onOpenChange, brickType, onSave }: EditBrickTypeDialogProps) => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const [formData, setFormData] = useState({
    name: brickType?.name || '',
    category: brickType?.category || '',
    grade: brickType?.grade || '',
    bricks_per_pallet: brickType?.bricks_per_pallet?.toString() || '',
    setting_rate: brickType?.setting_rate?.toString() || '',
    dehacking_rate: brickType?.dehacking_rate?.toString() || '',
    overtime_rate: brickType?.overtime_rate?.toString() || '',
    status: brickType?.status || 'active',
    brick_stage: brickType?.brick_stage || 'green'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    if (!brickType?.id) return;

    try {
      // Set user context before database operation
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { error } = await supabase
        .from('management_brick_types')
        .update({
          name: formData.name,
          category: formData.category,
          grade: formData.grade,
          bricks_per_pallet: parseInt(formData.bricks_per_pallet),
          setting_rate: parseFloat(formData.setting_rate),
          dehacking_rate: parseFloat(formData.dehacking_rate),
          overtime_rate: parseFloat(formData.overtime_rate),
          status: formData.status,
          brick_stage: formData.brick_stage
        })
        .eq('id', brickType.id);

      if (error) throw error;

      onSave();
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating brick type:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Brick Type</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input type="text" id="name" name="name" value={formData.name} onChange={handleInputChange} className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="category" className="text-right">
              Category
            </Label>
            <Input type="text" id="category" name="category" value={formData.category} onChange={handleInputChange} className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="grade" className="text-right">
              Grade
            </Label>
            <Input type="text" id="grade" name="grade" value={formData.grade} onChange={handleInputChange} className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="bricks_per_pallet" className="text-right">
              Bricks per Pallet
            </Label>
            <Input type="number" id="bricks_per_pallet" name="bricks_per_pallet" value={formData.bricks_per_pallet} onChange={handleInputChange} className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="setting_rate" className="text-right">
              Setting Rate
            </Label>
            <Input type="number" id="setting_rate" name="setting_rate" value={formData.setting_rate} onChange={handleInputChange} className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="dehacking_rate" className="text-right">
              Dehacking Rate
            </Label>
            <Input type="number" id="dehacking_rate" name="dehacking_rate" value={formData.dehacking_rate} onChange={handleInputChange} className="col-span-3" />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="overtime_rate" className="text-right">
              Overtime Rate
            </Label>
            <Input type="number" id="overtime_rate" name="overtime_rate" value={formData.overtime_rate} onChange={handleInputChange} className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Status
            </Label>
            <Select value={formData.status} onValueChange={(value) => handleInputChange({ target: { name: 'status', value } } as any)}>
              <SelectTrigger id="status" className="col-span-3">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="brick_stage" className="text-right">
              Brick Stage
            </Label>
            <Select value={formData.brick_stage} onValueChange={(value) => handleInputChange({ target: { name: 'brick_stage', value } } as any)}>
              <SelectTrigger id="brick_stage" className="col-span-3">
                <SelectValue placeholder="Select brick stage" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="green">Green</SelectItem>
                <SelectItem value="burnt">Burnt</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <Button onClick={handleSave}>Save changes</Button>
      </DialogContent>
    </Dialog>
  );
};
