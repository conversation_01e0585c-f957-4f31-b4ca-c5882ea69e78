
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAIForecasting } from '@/hooks/useAIForecasting';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { Calendar, TrendingUp, AlertTriangle, Lightbulb } from 'lucide-react';

export const ProductionForecasting = () => {
  const [timeRange, setTimeRange] = useState('30');
  const [forecastType, setForecastType] = useState('production');
  
  const { data: forecast, isLoading, refetch } = useAIForecasting({
    timeRange: parseInt(timeRange),
    type: forecastType
  });

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      case 'opportunity': return <Lightbulb className="h-4 w-4 text-blue-500" />;
      default: return <TrendingUp className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Forecast Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-center">
            <div className="space-y-2">
              <label className="text-sm font-medium">Time Range</label>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">7 days</SelectItem>
                  <SelectItem value="30">30 days</SelectItem>
                  <SelectItem value="90">90 days</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Forecast Type</label>
              <Select value={forecastType} onValueChange={setForecastType}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="production">Production Output</SelectItem>
                  <SelectItem value="demand">Market Demand</SelectItem>
                  <SelectItem value="costs">Operational Costs</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button onClick={() => refetch()} className="mt-6" disabled={isLoading}>
              {isLoading ? 'Generating...' : 'Generate Forecast'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Forecast Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Production Forecast - Next {timeRange} Days</CardTitle>
          <CardDescription>
            AI-generated predictions based on historical data and current trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          {forecast?.predictions && forecast.predictions.length > 0 ? (
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={forecast.predictions}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={(value) => new Date(value).toLocaleDateString()}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleDateString()}
                    formatter={(value, name) => [
                      value,
                      name === 'predicted' ? 'Predicted Production' : 'Historical Data'
                    ]}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="predicted" 
                    stroke="#82ca9d" 
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="predicted"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-80 flex items-center justify-center">
              <p className="text-muted-foreground">
                {isLoading ? 'Generating forecast based on your production data...' : 'No forecast data available. Try adjusting the time range or type.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Forecast Insights */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* AI Insights */}
        <Card>
          <CardHeader>
            <CardTitle>Forecast Insights</CardTitle>
            <CardDescription>
              Key predictions and recommendations from AI analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {forecast?.insights && forecast.insights.length > 0 ? forecast.insights.map((insight, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                {getInsightIcon(insight.type)}
                <div className="flex-1 space-y-1">
                  <div className="flex items-center gap-2">
                    <h4 className="text-sm font-semibold">{insight.title}</h4>
                    <Badge variant="outline" className="text-xs">
                      {insight.confidence}% confidence
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{insight.description}</p>
                </div>
              </div>
            )) : (
              <p className="text-muted-foreground">Generate a forecast to see AI insights about your production trends.</p>
            )}
          </CardContent>
        </Card>

        {/* Historical Performance Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Historical Performance</CardTitle>
            <CardDescription>
              Recent data used for forecasting
            </CardDescription>
          </CardHeader>
          <CardContent>
            {forecast ? (
              <div className="space-y-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <p className="text-2xl font-bold">{forecast.dataPoints || 0}</p>
                  <p className="text-sm text-muted-foreground">Historical records analyzed</p>
                </div>
                <div className="text-sm text-muted-foreground">
                  <p>Forecast generated from your actual production data including:</p>
                  <ul className="mt-2 space-y-1 list-disc list-inside">
                    <li>Production entries</li>
                    <li>Setting team performance</li>
                    <li>Dehacking records</li>
                    <li>Employee performance data</li>
                  </ul>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">Historical data will be displayed here after generating a forecast.</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
