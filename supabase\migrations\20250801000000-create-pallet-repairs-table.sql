-- Create pallet_repairs table for tracking daily pallet repair activities
CREATE TABLE public.pallet_repairs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date DATE NOT NULL,
  pallets_repaired INTEGER NOT NULL CHECK (pallets_repaired >= 0),
  broken_received INTEGER NOT NULL DEFAULT 0 CHECK (broken_received >= 0),
  broken_waiting INTEGER NOT NULL DEFAULT 0 CHECK (broken_waiting >= 0),
  factory_pallet_count INTEGER CHECK (factory_pallet_count >= 0),
  strapping_pallet_count INTEGER CHECK (strapping_pallet_count >= 0),
  repairer TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  user_id UUID REFERENCES public.users(id)
);

-- Create index for efficient querying by date
CREATE INDEX idx_pallet_repairs_date ON public.pallet_repairs(date DESC);

-- Create index for efficient querying by user
CREATE INDEX idx_pallet_repairs_user_id ON public.pallet_repairs(user_id);

-- Enable RLS
ALTER TABLE public.pallet_repairs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies that allow all operations (since app uses custom auth)
CREATE POLICY "Users can view pallet repairs" ON public.pallet_repairs
  FOR SELECT USING (true);

CREATE POLICY "Users can insert pallet repairs" ON public.pallet_repairs
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update pallet repairs" ON public.pallet_repairs
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete pallet repairs" ON public.pallet_repairs
  FOR DELETE USING (true);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_pallet_repairs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_pallet_repairs_updated_at
  BEFORE UPDATE ON public.pallet_repairs
  FOR EACH ROW
  EXECUTE FUNCTION update_pallet_repairs_updated_at();
