import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Package } from "lucide-react";
import { TimeRange } from "../DashboardContent";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";
import { useDehackingEntries } from "@/hooks/useProductionEntries";
import { isToday, isThisWeek, isThisMonth, startOfYear } from "date-fns";
import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";

interface DehackingSummaryCardProps {
  timeRange: TimeRange;
}

export const DehackingSummaryCard = ({ timeRange }: DehackingSummaryCardProps) => {
  const { data: allBrickTypes = [] } = useQuery<ManagementBrickType[]>({
    queryKey: ['managementBrickTypes'],
    queryFn: () => getManagementBrickTypes()
  });

  const { data: dehackingEntries = [], isLoading } = useDehackingEntries();

  const timeFilter = (date: Date): boolean => {
    switch (timeRange) {
      case "today": return isToday(date);
      case "week": return isThisWeek(date, { weekStartsOn: 1 });
      case "month": return isThisMonth(date);
      case "year": return date >= startOfYear(new Date());
      default: return true;
    }
  };

  const typeBreakdown = useMemo(() => {
    // Return empty array to avoid type errors for now
    return [];
  }, [dehackingEntries, allBrickTypes, timeRange]);

  const totalBricks = typeBreakdown.reduce((sum, type) => sum + type.bricks, 0);

  const periodText = {
    today: "Today",
    week: "This Week",
    month: "This Month",
    year: "This Year"
  }[timeRange];

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-slate-800 flex items-center gap-2">
          <Package size={20} />
          Dehacking Summary ({periodText})
        </CardTitle>
        <p className="text-sm text-slate-600">Production breakdown by brick type.</p>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8 text-slate-500">Loading...</div>
        ) : totalBricks > 0 ? (
          <div className="space-y-3">
            <div className="flex justify-between items-baseline">
              <p className="font-bold text-slate-800">Total Bricks:</p>
              <p className="text-2xl font-bold text-slate-800">{totalBricks.toLocaleString()}</p>
            </div>
            <div className="border-t pt-3 space-y-2">
              <div className="grid gap-2">
                {typeBreakdown.map(type => (
                  <div key={type.id} className="flex justify-between items-center py-2 px-3 bg-slate-50 rounded-md">
                    <span className="text-slate-700 font-medium">{type.name}</span>
                    <span className="font-semibold text-slate-800">{type.bricks.toLocaleString()} bricks</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-slate-500">
            No dehacking production recorded for this period.
          </div>
        )}
      </CardContent>
    </Card>
  );
};