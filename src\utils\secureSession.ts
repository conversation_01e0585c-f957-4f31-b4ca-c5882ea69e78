
import { supabase } from '@/integrations/supabase/client';
import { v4 as uuidv4 } from 'uuid';

export interface SecureSession {
  sessionToken: string;
  userId: string;
  expiresAt: Date;
}

// Generate a secure session token
export const generateSessionToken = (): string => {
  return uuidv4() + '-' + Date.now() + '-' + Math.random().toString(36).substring(2);
};

// Create a new secure session
export const createSecureSession = async (userId: string, userAgent?: string, ipAddress?: string): Promise<SecureSession> => {
  const sessionToken = generateSessionToken();
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  try {
    const { error } = await supabase
      .from('user_sessions')
      .insert({
        user_id: userId,
        session_token: sessionToken,
        expires_at: expiresAt.toISOString(),
        ip_address: ipAddress,
        user_agent: userAgent
      });

    if (error) {
      console.error('Failed to create session in database:', error);
      // Continue without database session if insertion fails
    }
  } catch (error) {
    console.error('Failed to create session:', error);
    // Continue without database session if insertion fails
  }

  return {
    sessionToken,
    userId,
    expiresAt
  };
};

// Validate a session token
export const validateSession = async (sessionToken: string): Promise<{ userId: string; role: string } | null> => {
  try {
    const { data, error } = await supabase
      .from('user_sessions')
      .select(`
        user_id,
        expires_at,
        users (
          id,
          role,
          active
        )
      `)
      .eq('session_token', sessionToken)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !data || !data.users) {
      console.log('Session validation failed:', error);
      return null;
    }

    const user = Array.isArray(data.users) ? data.users[0] : data.users;
    
    if (!user.active) {
      console.log('User account is inactive');
      return null;
    }

    // Update last accessed time
    try {
      await supabase
        .from('user_sessions')
        .update({ last_accessed: new Date().toISOString() })
        .eq('session_token', sessionToken);
    } catch (updateError) {
      console.error('Failed to update session last accessed time:', updateError);
      // Continue even if update fails
    }

    return {
      userId: user.id,
      role: user.role
    };
  } catch (error) {
    console.error('Session validation error:', error);
    return null;
  }
};

// Invalidate a session
export const invalidateSession = async (sessionToken: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('user_sessions')
      .delete()
      .eq('session_token', sessionToken);

    if (error) {
      console.error('Failed to invalidate session:', error);
    }
  } catch (error) {
    console.error('Failed to invalidate session:', error);
  }
};

// Clean up expired sessions
export const cleanupExpiredSessions = async (): Promise<void> => {
  try {
    const { error } = await supabase
      .from('user_sessions')
      .delete()
      .lt('expires_at', new Date().toISOString());

    if (error) {
      console.error('Failed to cleanup expired sessions:', error);
    }
  } catch (error) {
    console.error('Failed to cleanup expired sessions:', error);
  }
};
