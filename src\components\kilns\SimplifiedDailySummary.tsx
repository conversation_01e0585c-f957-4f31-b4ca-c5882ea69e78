
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { ChamberProductionTable } from "./ChamberProductionTable";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

const KILN_OPTIONS = [
  { id: "habla-kiln", name: "Habla" },
  { id: "kiln-1", name: "Kiln 1" },
  { id: "kiln-2", name: "Kiln 2" },
  { id: "kiln-3", name: "Kiln 3" },
  { id: "kiln-4", name: "Kiln 4" },
  { id: "kiln-5", name: "Kiln 5" },
];

const PARAMETER_TESTS = [
  'Brick Core Temp',
  'Brick Moisture',
  'CO',
  'CO₂',
  'Cooling Zone Temp',
  'Draught Pressure',
  'Fire Zone Temp',
  'O₂',
  'Preheat Temp'
];

export const SimplifiedDailySummary = () => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));

  const { data: dailyData = [], isLoading } = useQuery({
    queryKey: ['simplified-daily-summary', selectedDate],
    queryFn: async () => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      console.log('🔍 Fetching daily summary for date:', selectedDate);

      const { data, error } = await supabase
        .from('kiln_monitoring_measurements')
        .select('*')
        .eq('measurement_date', selectedDate)
        .order('measurement_time', { ascending: true });

      if (error) {
        console.error('Error fetching daily measurements:', error);
        return [];
      }

      console.log('📊 Daily measurements fetched:', data?.length || 0, 'entries');
      console.log('📊 Sample daily measurement:', data?.[0]);

      // Group by kiln, time intervals, and parameters
      const grouped = data.reduce((acc: any, measurement: any) => {
        const time = measurement.measurement_time;
        let interval = 1;
        
        // Determine interval based on time
        if (time >= '06:00' && time < '14:00') interval = 1;
        else if (time >= '14:00' && time < '22:00') interval = 2;
        else interval = 3;

        if (!acc[measurement.kiln_id]) {
          acc[measurement.kiln_id] = {};
        }
        
        if (!acc[measurement.kiln_id][interval]) {
          acc[measurement.kiln_id][interval] = {};
        }

        // Add parameter data
        Object.entries(measurement.parameters as Record<string, number>).forEach(([param, value]) => {
          if (!acc[measurement.kiln_id][interval][param]) {
            acc[measurement.kiln_id][interval][param] = [];
          }
          acc[measurement.kiln_id][interval][param].push(value);
        });

        return acc;
      }, {});

      return grouped;
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
    refetchInterval: 30000, // Refetch every 30 seconds for live data
  });

  return (
    <div className="space-y-6">
      {/* Chamber Production Tracking Table */}
      <ChamberProductionTable selectedDate={selectedDate} />

      {/* Original Daily Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Daily Summary - Simplified</CardTitle>
          <div className="flex items-center gap-4">
            <Label htmlFor="date">Date:</Label>
            <Input
              id="date"
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-40"
            />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {PARAMETER_TESTS.map((test) => (
              <div key={test}>
                <h3 className="font-semibold mb-2">{test}</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Kiln</TableHead>
                      <TableHead className="text-center">Interval 1</TableHead>
                      <TableHead className="text-center">Interval 2</TableHead>
                      <TableHead className="text-center">Interval 3</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {KILN_OPTIONS.map((kiln) => (
                      <TableRow key={kiln.id}>
                        <TableCell className="font-medium">{kiln.name}</TableCell>
                        <TableCell className="text-center">
                          {dailyData[kiln.id]?.[1]?.[test] ?
                            Math.round(dailyData[kiln.id][1][test].reduce((a: number, b: number) => a + b, 0) / dailyData[kiln.id][1][test].length * 10) / 10 :
                            '-'
                          }
                        </TableCell>
                        <TableCell className="text-center">
                          {dailyData[kiln.id]?.[2]?.[test] ?
                            Math.round(dailyData[kiln.id][2][test].reduce((a: number, b: number) => a + b, 0) / dailyData[kiln.id][2][test].length * 10) / 10 :
                            '-'
                          }
                        </TableCell>
                        <TableCell className="text-center">
                          {dailyData[kiln.id]?.[3]?.[test] ?
                            Math.round(dailyData[kiln.id][3][test].reduce((a: number, b: number) => a + b, 0) / dailyData[kiln.id][3][test].length * 10) / 10 :
                            '-'
                          }
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
