import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Flame, Loader2, Calendar } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { KilnConfig, FireConfig } from "@/data/kilnData";
import { useKilns } from "@/hooks/useKilns";
import { getManagementBrickTypes } from "@/data/managementBrickTypes";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { subscribeToDehacking } from "@/data/dehackingStore";
import { KilnSummaryTables } from "../kilns/KilnSummaryTables";

// Chamber component with toggle functionality for setting
const SettingChamber = ({
  number,
  fireName,
  brickCount,
  brickTypes,
  isBurning,
  onToggle,
  isLoading,
  dataAge,
  onClearData
}: {
  number: number;
  fireName?: string;
  brickCount?: number;
  brickTypes?: string[];
  isBurning: boolean;
  onToggle: (isBurning: boolean) => void;
  isLoading: boolean;
  dataAge?: number | null;
  onClearData: () => void;
}) => {
  return (
    <div className={`border rounded p-3 min-h-[80px] text-center flex flex-col items-center justify-between ${
      isBurning ? 'bg-orange-50 border-orange-200' : 'bg-slate-50'
    }`}>
      <div className="flex flex-col items-center flex-1">
        <div className="text-sm font-medium mb-1">
          Chamber {number}
        </div>
        <div className={`text-${brickCount ? 'blue' : 'gray'}-700 font-bold text-xl mb-1`}>
          {brickCount ? brickCount.toLocaleString() : '-'}
        </div>
        {brickTypes && brickTypes.length > 0 ? (
          <div className="text-xs text-slate-600 text-center">
            {brickTypes.join(", ")}
          </div>
        ) : null}
      </div>
      <div className="flex items-center gap-2 mt-2">
        <Flame
          size={16}
          className={isBurning ? "text-orange-500" : "text-slate-400"}
        />
        <Switch
          checked={isBurning}
          onCheckedChange={onToggle}
          disabled={isLoading}
          className="data-[state=checked]:bg-orange-500"
        />
        <Label className="text-xs text-slate-600">
          {isBurning ? "Burning" : "Off"}
        </Label>
      </div>
    </div>
  );
};

// Chamber component for dehacking with checkbox
const DehackingChamber = ({
  number,
  fireName,
  brickCount,
  brickTypes,
  isSelected,
  onSelect,
  kilnId,
  dataAge,
  onClearData
}: {
  number: number;
  fireName?: string;
  brickCount?: number;
  brickTypes?: string[];
  isSelected: boolean;
  onSelect: (checked: boolean) => void;
  kilnId: string;
  dataAge?: number | null;
  onClearData: () => void;
}) => {
  return (
    <div className={`border rounded p-3 min-h-[80px] text-center flex flex-col items-center justify-between ${
      isSelected ? 'bg-green-50 border-green-200' : 'bg-slate-50'
    }`}>
      <div className="flex flex-col items-center flex-1">
        <div className="text-sm font-medium mb-1">
          Chamber {number}
        </div>
        <div className={`text-${brickCount ? 'blue' : 'gray'}-700 font-bold text-xl mb-1`}>
          {brickCount ? brickCount.toLocaleString() : '-'}
        </div>
        {brickTypes && brickTypes.length > 0 ? (
          <div className="text-xs text-slate-600 text-center">
            {brickTypes.join(", ")}
          </div>
        ) : null}
      </div>
      <div className="flex items-center gap-2 mt-2">
        <Checkbox 
          checked={isSelected}
          onCheckedChange={onSelect}
          id={`dehacking-${kilnId}-${number}`}
          className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
        />
        <Label 
          htmlFor={`dehacking-${kilnId}-${number}`}
          className="text-xs text-slate-600 cursor-pointer"
        >
          {isSelected ? "Dehacking" : "Available"}
        </Label>
      </div>
    </div>
  );
};

// Interface for chamber fire status
interface ChamberFireStatus {
  id: number | string;
  kiln_id: string;
  chamber_number: number;
  is_burning: boolean;
}

interface SettingProductionEntry {
  id: number;
  brick_type_id: string;
  pallet_count: number;
  fire_id?: string;
  chamber_number?: number;
  date: string;
  management_brick_types?: {
    name: string;
    bricks_per_pallet: number;
  };
  fires?: {
    kiln_id: string;
  };
  [key: string]: any;
}

interface DehackingEntry {
  id: number;
  brick_type_id: string;
  pallet_count: number;
  fire_id?: string;
  chamber_number?: number;
  date: string;
  management_brick_types?: {
    name: string;
    bricks_per_pallet: number;
  };
  fires?: {
    kiln_id: string;
  };
  [key: string]: any;
}

interface ChamberData {
  chamberNumber: number;
  chamberCode?: string;
  brickCount: number;
  brickTypes: string[];
  fireName?: string;
  isBurning: boolean;
  dataAge?: number | null;
}

// Helper function to get chamber count for each kiln
const getChamberCount = (kilnName: string): number => {
  if (kilnName === 'Habla') {
    return 10;
  }
  // Kiln 1-5 should have 24 chambers each
  return 24;
};

export const ChamberDashboard = () => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  const [selectedDehackingChambers, setSelectedDehackingChambers] = useState<Set<string>>(() => {
    // Load from localStorage on component mount
    const stored = localStorage.getItem('selectedDehackingChambers');
    return stored ? new Set(JSON.parse(stored)) : new Set();
  });

  // State for date filtering - default to current date but allow user control
  const [filterDate, setFilterDate] = useState(() => {
    // Check if user has set a custom date, otherwise use current date
    const storedDate = localStorage.getItem('chamberDashboardFilterDate');
    return storedDate || new Date().toISOString().split('T')[0];
  });

  // Use React Query to fetch kilns data with proper user context
  const { data: kilnsData = [], isLoading: kilnsLoading, isError: kilnsError } = useKilns();

  // Sort kilns: Habla first, then Kiln 1-5
  const kilns = kilnsData.sort((a, b) => {
    if (a.name === 'Habla') return -1;
    if (b.name === 'Habla') return 1;

    const aNum = parseInt(a.name.replace('Kiln ', ''));
    const bNum = parseInt(b.name.replace('Kiln ', ''));
    return aNum - bNum;
  });

  const { data: brickTypes = [], isLoading: brickTypesLoading } = useQuery({
    queryKey: ['brickTypes'],
    queryFn: async () => {
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getManagementBrickTypes(userId || undefined);
    },
  });

  // Calculate date range for 7-day window
  const getDateRange = (baseDate: string) => {
    const base = new Date(baseDate);
    const endDate = new Date(base);
    const startDate = new Date(base);
    startDate.setDate(startDate.getDate() - 7); // 7 days back from base date

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    };
  };

  const { data: settingEntries = [], isLoading: settingEntriesLoading } = useQuery({
    queryKey: ['setting-production-entries', filterDate],
    queryFn: async () => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { startDate, endDate } = getDateRange(filterDate);

      const { data, error } = await supabase
        .from('setting_production_entries')
        .select('*, management_brick_types(name, bricks_per_pallet), fires(kiln_id)')
        .gte('date', startDate)
        .lte('date', endDate);

      if (error) {
        console.error("Error fetching setting entries:", error);
        return [];
      }

      return data || [];
    },
  });

  const { data: dehackingEntries = [], isLoading: dehackingEntriesLoading } = useQuery({
    queryKey: ['dehacking-entries', filterDate],
    queryFn: async () => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { startDate, endDate } = getDateRange(filterDate);

      const { data, error } = await supabase
        .from('dehacking_entries')
        .select('*, management_brick_types(name, bricks_per_pallet), fires(kiln_id)')
        .gte('date', startDate)
        .lte('date', endDate);

      if (error) {
        console.error("Error fetching dehacking entries:", error);
        return [];
      }

      return data || [];
    },
  });

  const { data: chamberFireStatus = [], isLoading: chamberStatusLoading } = useQuery({
    queryKey: ['chamber-fire-status'],
    queryFn: async () => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { data, error } = await supabase
        .from('chamber_fire_status')
        .select('*');
      
      if (error) {
        console.error("Error fetching chamber fire status:", error);
        throw error;
      }
      
      return (data || []) as ChamberFireStatus[];
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
    refetchInterval: 1000,
    refetchOnMount: true,
  });

  // Mutation for updating fire status
  const updateFireStatusMutation = useMutation({
    mutationFn: async ({ kilnId, chamberNumber, isBurning }: { kilnId: string; chamberNumber: number; isBurning: boolean }) => {
      console.log(`🔥 Updating fire status for kiln ${kilnId}, chamber ${chamberNumber} to ${isBurning}`);

      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { data, error } = await supabase
        .from('chamber_fire_status')
        .upsert({
          kiln_id: kilnId,
          chamber_number: chamberNumber,
          is_burning: isBurning,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'kiln_id,chamber_number'
        })
        .select();

      if (error) {
        console.error("❌ Fire status update failed:", error);
        throw error;
      }

      console.log("✅ Fire status updated successfully:", data);
      return data;
    },
    onSuccess: (data, variables) => {
      console.log("🎉 Mutation successful");
      toast.success(`Chamber ${variables.chamberNumber} fire ${variables.isBurning ? 'turned on' : 'turned off'}`);
    },
    onError: (error, variables) => {
      console.error("❌ Fire status update mutation failed:", error);
      toast.error(`Failed to update Chamber ${variables.chamberNumber} fire status`);
    },
  });

  // Function to clear setting data for a specific chamber
  const clearSettingData = async (kilnId: string, chamberNumber: number) => {
    try {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { error } = await supabase
        .from('setting_production_entries')
        .delete()
        .eq('fire_id', kilnId)
        .eq('chamber_number', chamberNumber);

      if (error) {
        console.error('Error clearing setting data:', error);
      }
    } catch (error) {
      console.error('Error clearing setting data:', error);
    }
  };

  // Function to clear dehacking data for a specific chamber
  const clearDehackingData = async (kilnId: string, chamberNumber: number) => {
    try {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { error } = await supabase
        .from('dehacking_entries')
        .delete()
        .eq('fire_id', kilnId)
        .eq('chamber_number', chamberNumber);

      if (error) {
        console.error('Error clearing dehacking data:', error);
      }
    } catch (error) {
      console.error('Error clearing dehacking data:', error);
    }
  };

  // Show loading state
  if (kilnsLoading || brickTypesLoading || chamberStatusLoading || settingEntriesLoading || dehackingEntriesLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
        <p className="ml-2 text-slate-500">Loading chamber dashboard...</p>
      </div>
    );
  }

  if (kilnsError) {
    return (
      <div className="text-center py-12 text-red-500">
        <p>Failed to load chamber data. Please try again later.</p>
      </div>
    );
  }

  // Updated processKilnData function with 7-day data retention logic
  const processKilnData = (kiln: any) => {
    const settingChambers: ChamberData[] = [];
    const dehackingChambers: ChamberData[] = [];
    
    const chamberCount = getChamberCount(kiln.name);
    
    for (let i = 1; i <= chamberCount; i++) {
      // Get burning status
      const status = chamberFireStatus.find(
        s => s.kiln_id === kiln.id && s.chamber_number === i
      );
      
      // Get setting entries for this chamber - implement 7-day retention
      const settingEntriesForChamber = settingEntries.filter(
        entry => entry.fires?.kiln_id === kiln.id && entry.chamber_number === i
      );

      // Group by date and get cumulative counts for 7-day window
      const settingByDate = settingEntriesForChamber.reduce((acc, entry) => {
        const entryDate = entry.date;
        const daysDiff = Math.floor((new Date(filterDate).getTime() - new Date(entryDate).getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysDiff <= 7) {
          if (!acc[entryDate]) acc[entryDate] = [];
          acc[entryDate].push(entry);
        }
        return acc;
      }, {} as Record<string, any[]>);

      // Calculate cumulative brick count for the 7-day window
      const settingBrickCount = Object.values(settingByDate).flat().reduce((sum, entry) => {
        const bricksPerPallet = entry.management_brick_types?.bricks_per_pallet || 0;
        return sum + (entry.pallet_count * bricksPerPallet);
      }, 0);

      const settingBrickTypes = [...new Set(Object.values(settingByDate).flat().map(entry => entry.management_brick_types?.name).filter(Boolean))];
      
      // Get dehacking entries for this chamber - implement 7-day retention
      const dehackingEntriesForChamber = dehackingEntries.filter(
        entry => entry.fires?.kiln_id === kiln.id && entry.chamber_number === i
      );

      // Group by date and get cumulative counts for 7-day window
      const dehackingByDate = dehackingEntriesForChamber.reduce((acc, entry) => {
        const entryDate = entry.date;
        const daysDiff = Math.floor((new Date(filterDate).getTime() - new Date(entryDate).getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysDiff <= 7) {
          if (!acc[entryDate]) acc[entryDate] = [];
          acc[entryDate].push(entry);
        }
        return acc;
      }, {} as Record<string, any[]>);

      // Calculate cumulative brick count for the 7-day window
      const dehackingBrickCount = Object.values(dehackingByDate).flat().reduce((sum, entry) => {
        const bricksPerPallet = entry.management_brick_types?.bricks_per_pallet || 0;
        return sum + (entry.pallet_count * bricksPerPallet);
      }, 0);

      const dehackingBrickTypes = [...new Set(Object.values(dehackingByDate).flat().map(entry => entry.management_brick_types?.name).filter(Boolean))];
      
      // Calculate data age (oldest date in the 7-day window)
      const settingDates = Object.keys(settingByDate);
      const dehackingDates = Object.keys(dehackingByDate);
      
      const oldestSettingDate = settingDates.length > 0 ? settingDates.sort()[0] : null;
      const oldestDehackingDate = dehackingDates.length > 0 ? dehackingDates.sort()[0] : null;
      
      const settingDataAge = oldestSettingDate ? 
        Math.floor((new Date(filterDate).getTime() - new Date(oldestSettingDate).getTime()) / (1000 * 60 * 60 * 24)) : null;
      const dehackingDataAge = oldestDehackingDate ? 
        Math.floor((new Date(filterDate).getTime() - new Date(oldestDehackingDate).getTime()) / (1000 * 60 * 60 * 24)) : null;

      settingChambers.push({
        chamberNumber: i,
        brickCount: settingBrickCount,
        brickTypes: settingBrickTypes,
        fireName: undefined,
        isBurning: status?.is_burning || false,
        dataAge: settingDataAge
      });

      dehackingChambers.push({
        chamberNumber: i,
        brickCount: dehackingBrickCount,
        brickTypes: dehackingBrickTypes,
        fireName: undefined,
        isBurning: false,
        dataAge: dehackingDataAge
      });
    }
    
    return { settingChambers, dehackingChambers };
  };

  // Handler for fire toggle in setting section
  const handleFireToggle = (kilnId: string, chamberNumber: number, isBurning: boolean) => {
    updateFireStatusMutation.mutate({ kilnId, chamberNumber, isBurning });
  };

  const handleDehackingSelect = (kilnId: string, chamberNumber: number, checked: boolean) => {
    const key = `${kilnId}-${chamberNumber}`;
    setSelectedDehackingChambers(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(key);
        // Clear setting data for this chamber when dehacking is selected
        clearSettingData(kilnId, chamberNumber);
      } else {
        newSet.delete(key);
      }
      // Save to localStorage
      localStorage.setItem('selectedDehackingChambers', JSON.stringify(Array.from(newSet)));
      return newSet;
    });

    console.log(`📦 Dehacking chamber ${chamberNumber} ${checked ? 'selected' : 'deselected'}`);
  };

  // Handle date change
  const handleDateChange = (newDate: string) => {
    setFilterDate(newDate);
    localStorage.setItem('chamberDashboardFilterDate', newDate);
  };

  return (
    <div className="space-y-6">
      {/* Summary Tables at the top */}
      <KilnSummaryTables />

      {/* Date Control */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Chamber Dashboard
            </span>
            <div className="flex items-center gap-2">
              <Label htmlFor="filter-date" className="text-sm font-medium">
                Filter Date:
              </Label>
              <Input
                id="filter-date"
                type="date"
                value={filterDate}
                onChange={(e) => handleDateChange(e.target.value)}
                className="w-40"
              />
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-slate-600">
            <p>
              <strong>Showing data for:</strong> {new Date(filterDate).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
            <p className="mt-2">
              <strong>7-Day Data Retention:</strong> Data shows cumulative counts for up to 7 days.
              New data after 7 days replaces old data, with cumulative effect for the prior period.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Kiln Chambers */}
      <div className="space-y-8">
        {kilns.map(kiln => {
          const { settingChambers, dehackingChambers } = processKilnData(kiln);

          return (
            <Card key={kiln.id}>
              <CardHeader>
                <CardTitle>{kiln.name} - Chamber Dashboard</CardTitle>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold text-slate-800 mb-2">Setting</h3>
                <div className="grid grid-cols-4 gap-4 mb-6">
                  {settingChambers.map((chamber) => (
                    <SettingChamber
                      key={`${kiln.id}-setting-chamber-${chamber.chamberNumber}`}
                      number={chamber.chamberNumber}
                      fireName={chamber.fireName}
                      brickCount={chamber.brickCount}
                      brickTypes={chamber.brickTypes}
                      isBurning={chamber.isBurning}
                      onToggle={(isBurning) => handleFireToggle(kiln.id, chamber.chamberNumber, isBurning)}
                      isLoading={updateFireStatusMutation.isPending}
                      dataAge={chamber.dataAge}
                      onClearData={() => clearSettingData(kiln.id, chamber.chamberNumber)}
                    />
                  ))}
                </div>

                <h3 className="font-semibold text-slate-800 mb-2">Dehacking</h3>
                <div className="grid grid-cols-4 gap-4">
                  {dehackingChambers.map((chamber) => (
                    <DehackingChamber
                      key={`${kiln.id}-dehacking-chamber-${chamber.chamberNumber}`}
                      number={chamber.chamberNumber}
                      fireName={chamber.fireName}
                      brickCount={chamber.brickCount}
                      brickTypes={chamber.brickTypes}
                      isSelected={selectedDehackingChambers.has(`${kiln.id}-${chamber.chamberNumber}`)}
                      onSelect={(checked) => handleDehackingSelect(kiln.id, chamber.chamberNumber, checked)}
                      kilnId={kiln.id}
                      dataAge={chamber.dataAge}
                      onClearData={() => clearDehackingData(kiln.id, chamber.chamberNumber)}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default ChamberDashboard;
