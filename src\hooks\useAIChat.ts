
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

export const useAIChat = () => {
  const [isLoading, setIsLoading] = useState(false);

  const sendMessage = async (message: string) => {
    setIsLoading(true);
    try {
      console.log('Sending message to AI chat:', message);
      
      const { data, error } = await supabase.functions.invoke('ai-business-intelligence', {
        body: { 
          type: 'chat',
          params: { message }
        }
      });

      if (error) {
        console.error('Error calling AI chat:', error);
        throw error;
      }

      return data?.data || {
        content: 'Sorry, I encountered an error processing your request.',
        suggestions: ['Try asking again', 'Check system status']
      };
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    sendMessage,
    isLoading
  };
};
