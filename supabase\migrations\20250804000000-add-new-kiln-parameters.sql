-- Migration to add new kiln monitoring parameters
-- This adds parameter norms for the new temperature zones and measurement parameters

-- Insert new parameter norms for the additional parameters
INSERT INTO public.kiln_parameter_norms (parameter_name, unit, min_value, max_value, cause, action, reasoning, action_required) VALUES
-- Temperature zones
('Pre-heat Zone 1 Temp', '°C', 200, 350, 'Insufficient heat transfer or poor fuel distribution', 'Check fuel supply and adjust air flow', 'Pre-heat zone 1 is critical for initial brick warming and moisture removal. Too low temperatures result in thermal shock, too high can cause rapid moisture expansion.', 'Monitor fuel flow rates and adjust primary air dampers'),
('Pre-heat Zone 2 Temp', '°C', 350, 500, 'Heat transfer issues or combustion problems', 'Adjust fuel-air ratio and check heat exchangers', 'Pre-heat zone 2 continues the gradual heating process. Proper temperature ensures even heat distribution and prevents cracking.', 'Verify secondary air settings and inspect heat transfer surfaces'),
('Pre-combustion Zone Temp', '°C', 500, 700, 'Combustion air imbalance or fuel quality issues', 'Check combustion air supply and fuel quality', 'Pre-combustion zone prepares bricks for the main firing process. Temperature control is crucial for proper chemical reactions.', 'Test fuel composition and adjust combustion air ratios'),
('Cooling Zone 1 Temp', '°C', 400, 600, 'Insufficient cooling air or heat retention issues', 'Adjust cooling fans and check air circulation', 'Cooling zone 1 begins the controlled cooling process. Too rapid cooling causes thermal stress and cracking.', 'Monitor cooling air flow rates and adjust fan speeds'),
('Cooling Zone 2 Temp', '°C', 200, 400, 'Cooling system malfunction or air flow problems', 'Check cooling system operation and air ducts', 'Cooling zone 2 completes the cooling process. Proper temperature ensures structural integrity of fired bricks.', 'Inspect cooling air distribution system and verify fan operation'),

-- Operational parameters
('Fire Position in Chamber', 'm', 1, 24, 'Fire advancement issues or fuel distribution problems', 'Check fuel feed system and fire progression', 'Fire position indicates the burning front location in the chamber. Proper progression ensures even firing of all bricks.', 'Monitor fire advancement rate and adjust fuel distribution'),
('Fire Movement', 'm/day', 2.0, 4.0, 'Fuel supply issues or draught problems', 'Adjust fuel rate and check draught system', 'Fire movement rate affects firing quality and cycle time. Too slow wastes fuel, too fast causes uneven firing.', 'Calculate optimal fire speed based on brick type and chamber conditions'),
('O2', '%', 2, 6, 'Excess air or combustion inefficiency', 'Adjust air-fuel ratio for optimal combustion', 'Oxygen levels indicate combustion efficiency. Too high wastes energy, too low causes incomplete combustion.', 'Monitor and adjust primary and secondary air flows'),
('CO2', '%', 8, 12, 'Combustion air imbalance or fuel quality', 'Optimize fuel-air mixture and check fuel quality', 'Carbon dioxide levels reflect combustion completeness. Proper levels indicate efficient fuel utilization.', 'Analyze flue gas composition and adjust combustion parameters'),
('Draught Pressure', 'mmWC', -10, -2, 'Fan operation issues or system blockages', 'Check fan operation and clear any blockages', 'Draught pressure drives combustion air flow and flue gas removal. Proper pressure ensures stable combustion.', 'Inspect fan systems, dampers, and flue gas pathways'),

-- Setting parameters
('Fuel to Brick Ratio - Setting', 'kg/1000 bricks', 80, 120, 'Fuel efficiency issues or brick density variations', 'Optimize fuel consumption based on brick type and firing requirements', 'Fuel to brick ratio determines firing cost and efficiency. Proper ratio ensures complete firing while minimizing fuel waste.', 'Calculate optimal fuel consumption based on brick specifications and firing curve'),
('Brick Moisture % - Setting', '%', 8, 12, 'Drying process issues or storage conditions', 'Improve drying process and storage conditions', 'Brick moisture content affects firing quality and energy consumption. Proper moisture prevents cracking and ensures even firing.', 'Monitor drying conditions and adjust storage environment')

ON CONFLICT (parameter_name) DO UPDATE SET
  unit = EXCLUDED.unit,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  cause = EXCLUDED.cause,
  action = EXCLUDED.action,
  reasoning = EXCLUDED.reasoning,
  action_required = EXCLUDED.action_required,
  updated_at = NOW();

-- Remove the old Brick Core Temp / CO parameter if it exists
DELETE FROM public.kiln_parameter_norms 
WHERE parameter_name IN ('Brick Core Temp / CO', 'Brick Core Temp', 'CO');

-- Update existing Fire Zone Temp parameter to ensure it's properly configured
UPDATE public.kiln_parameter_norms 
SET 
  min_value = 850,
  max_value = 950,
  cause = 'Fuel-air imbalance or combustion issues',
  action = 'Adjust fuel-air mixture and check combustion system',
  reasoning = 'Fire zone temperature is critical for proper brick firing. Too low results in underfired bricks, too high can cause deformation or cracking.',
  action_required = 'Monitor combustion parameters and adjust fuel-air ratio for optimal firing temperature',
  updated_at = NOW()
WHERE parameter_name = 'Fire Zone Temp';
