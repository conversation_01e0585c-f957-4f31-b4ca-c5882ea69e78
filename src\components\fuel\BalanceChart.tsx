
import { useState, useMemo, useEffect } from "react";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid } from "recharts";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { getFuelBunkers, type FuelBunker } from "@/data/fuelBunkersData";
import { getFuelTransactions } from "@/data/fuelTransactionsStore";
import { format } from "date-fns";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";

interface BalanceChartProps {}

function getDaysInPast(n: number) {
  const arr = [];
  const now = new Date();
  for(let i=n-1; i>=0; i--) {
    const d = new Date(now);
    d.setDate(now.getDate() - i);
    arr.push(d);
  }
  return arr;
}

export const BalanceChart = ({}: BalanceChartProps) => {
  const { data: fuelBunkersData = [], isLoading: isLoadingBunkers } = useQuery<FuelBunker[]>({
    queryKey: ['fuelBunkers'],
    queryFn: () => getFuelBunkers(),
  });

  const [selectedBunker, setSelectedBunker] = useState("");

  useEffect(() => {
    if (fuelBunkersData.length > 0 && !selectedBunker) {
      setSelectedBunker(fuelBunkersData[0].id);
    }
  }, [fuelBunkersData, selectedBunker]);

  const days = getDaysInPast(7);
  const txns = getFuelTransactions();

  // Initial bunker lookup (start with known current day)
  const bunkerObj = fuelBunkersData.find((b) => b.id === selectedBunker);
  const baseBalance = bunkerObj?.current_level ?? 0;

  // Memoize chart data for performance
  const chartData = useMemo(() => {
    // Calculate backwards from today
    let closing = baseBalance;
    const arr = [];
    // Reverse order (oldest to newest)
    for (let i = days.length - 1; i >= 0; i--) {
      const date = days[i];
      // Filter transactions for this date
      const dayTxns = txns.filter(
        (t) =>
          "bunkerId" in t &&
          t.bunkerId === selectedBunker &&
          format(new Date(t.date), "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
      );
      let netChange = 0;
      for (const t of dayTxns) {
        if (t.type === "delivery") {
          netChange += t.quantity;
        } else if (t.type === "dispensing") {
          netChange -= t.litresFilled;
        }
      }
      // Opening balance is closing minus the day's net change
      const opening = closing - netChange;
      arr.unshift({
        date: format(date, "EEE"),
        opening,
        closing
      });
      closing = opening;
    }
    return arr;
  }, [selectedBunker, baseBalance, days, txns]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fuel Bunker Daily Balances</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoadingBunkers ? (
          <div className="flex items-center justify-center h-[300px]">
            <Loader2 className="animate-spin h-6 w-6" />
          </div>
        ) : (
        <>
          <div className="mb-4 flex gap-2 items-center">
            <span className="text-sm text-slate-600">Bunker:</span>
            <Select value={selectedBunker} onValueChange={setSelectedBunker} disabled={!selectedBunker}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select bunker" />
              </SelectTrigger>
              <SelectContent>
                {fuelBunkersData.map((b) => (
                  <SelectItem value={b.id} key={b.id}>
                    {b.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="opening" fill="#6366f1" name="Opening (L)" />
              <Bar dataKey="closing" fill="#22d3ee" name="Closing (L)" />
            </BarChart>
          </ResponsiveContainer>
        </>
        )}
      </CardContent>
    </Card>
  );
};
