import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { verifyPassword } from '@/utils/passwordVerification';

interface LoginCredentials {
  username: string;
  password: string;
}

interface UserProfile {
  id: string;
  username: string;
  full_name?: string;
  email?: string;
  role: string;
  active: boolean;
}

interface AuthContextType {
  currentUser: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  getSessionDuration: () => string;
  loginTime: Date | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loginTime, setLoginTime] = useState<Date | null>(null);

  // Check for existing session on app load
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedUser = localStorage.getItem('currentUser');
        const storedLoginTime = localStorage.getItem('loginTime');

        if (storedUser) {
          const userData = JSON.parse(storedUser);
          console.log('Found stored user data:', userData);

          // Restore login time if available
          if (storedLoginTime) {
            setLoginTime(new Date(storedLoginTime));
          }

          // For hardcoded test users, skip database verification
          if (userData.id === 'admin-test-id') {
            console.log('Using hardcoded test user, skipping database verification');
            setCurrentUser(userData);
            setIsAuthenticated(true);
          } else {
            // Verify user still exists and is active for real database users
            const { data: user, error } = await supabase
              .from('users')
              .select('*')
              .eq('id', userData.id)
              .eq('active', true)
              .single();

            if (!error && user) {
              setCurrentUser({
                id: user.id,
                username: user.username,
                full_name: user.full_name,
                email: user.email,
                role: user.role,
                active: user.active,
              });
              setIsAuthenticated(true);
            } else {
              // Clear invalid session
              console.log('User verification failed, clearing session');
              localStorage.removeItem('currentUser');
            }
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        localStorage.removeItem('currentUser');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('Attempting login for:', credentials.username);

      // For testing, let's create a hardcoded admin user if credentials match
      if (credentials.username.trim().toLowerCase() === 'admin' && credentials.password === 'admin123') {
        console.log('Using hardcoded admin credentials');

        const userProfile: UserProfile = {
          id: 'admin-test-id',
          username: 'admin',
          full_name: 'Administrator',
          email: '<EMAIL>',
          role: 'admin',
          active: true,
        };

        // Set auth state
        setCurrentUser(userProfile);
        setIsAuthenticated(true);
        setLoginTime(new Date());

        // Store session
        localStorage.setItem('currentUser', JSON.stringify(userProfile));
        localStorage.setItem('loginTime', new Date().toISOString());

        console.log('Login successful - user profile set:', userProfile);
        console.log('Authentication state updated - isAuthenticated should be true');

        return { success: true };
      }

      // Since RLS is disabled, use direct table query for authentication

      let userData = null;
      let queryError = null;

      try {
        // Direct query to users table (RLS disabled)
        const { data: directData, error: directError } = await supabase
          .from('users')
          .select('*')
          .eq('username', credentials.username.trim())
          .eq('active', true)
          .single();

        userData = directData;
        queryError = directError;

      } catch (error) {
        queryError = error;
      }

      if (queryError) {
        // If it's an RLS error, provide more specific feedback
        if (queryError.message?.includes('row-level security')) {
          return { success: false, error: 'Database access restricted. Please contact administrator.' };
        }
        return { success: false, error: 'Invalid username or password' };
      }

      if (!userData) {
        return { success: false, error: 'Invalid username or password' };
      }

      // Password verification using the comprehensive password verification utility
      if (!userData.password_hash) {
        return { success: false, error: 'Invalid username or password' };
      }

      const verificationResult = await verifyPassword(credentials.password, userData.password_hash);

      if (!verificationResult.isValid) {
        return { success: false, error: 'Invalid username or password' };
      }

      // Create user profile
      const userProfile: UserProfile = {
        id: userData.id,
        username: userData.username,
        full_name: userData.full_name,
        email: userData.email,
        role: userData.role,
        active: userData.active,
      };

      // Set auth state
      setCurrentUser(userProfile);
      setIsAuthenticated(true);

      // Store session
      localStorage.setItem('currentUser', JSON.stringify(userProfile));

      return { success: true };

    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'An error occurred during login' };
    }
  };

  const logout = () => {
    setCurrentUser(null);
    setLoginTime(null);
    localStorage.removeItem('currentUser');
    localStorage.removeItem('loginTime');
  };

  const getSessionDuration = (): string => {
    if (!loginTime) return '0 minutes';

    const now = new Date();
    const diffMs = now.getTime() - loginTime.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);

    if (diffHours > 0) {
      const remainingMinutes = diffMinutes % 60;
      return `${diffHours}h ${remainingMinutes}m`;
    }

    return `${diffMinutes} minutes`;
  };

  const value: AuthContextType = {
    currentUser,
    isAuthenticated: !!currentUser,
    isLoading,
    login,
    logout,
    getSessionDuration,
    loginTime,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
