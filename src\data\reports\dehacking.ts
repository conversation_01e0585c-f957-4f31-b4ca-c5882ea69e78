
import { supabase, setUserContext } from "@/integrations/supabase/client";
import type { ReportData } from "./types";

export const getDehackingReport = async ({ from, to, userId }: { from: string, to: string, userId?: string }): Promise<ReportData> => {
  if (userId) {
    await setUserContext(userId);
  }

  const { data: entries, error } = await supabase
    .from('dehacking_entries')
    .select(`*, employees(name)`)
    .gte('date', from)
    .lte('date', to);
  if (error) throw error;
  
  // Note: Dehacking is individual. We'll show stats per employee.
  const byEmployee = entries.reduce((acc, entry) => {
    const employee = entry.employees as { name: string };
    if (!employee) return acc;
    if (!acc[entry.employee_id]) acc[entry.employee_id] = { name: employee.name, pallets_dehacked: 0, target: 40 };
    acc[entry.employee_id].pallets_dehacked += entry.pallet_count;
    return acc;
  }, {} as Record<string, { name: string, pallets_dehacked: number, target: number, efficiency?: number }>);

  Object.values(byEmployee).forEach(emp => {
      emp.efficiency = emp.target > 0 ? Math.round((emp.pallets_dehacked / emp.target) * 100) : 0;
  });
  
  return { main: Object.values(byEmployee), secondary: [] }; // No secondary data for now
};
