
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { format } from 'date-fns';
import { LoadPlanningEntry } from '@/types/loadPlanning';
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

export interface UseLoadPlanningOptions {
  dateFrom?: Date;
  dateTo?: Date;
}

export const useLoadPlanning = (options?: UseLoadPlanningOptions) => {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  const { data: loads = [], isLoading, error, refetch } = useQuery({
    queryKey: ['load-planning', options?.dateFrom, options?.dateTo],
    queryFn: async () => {
      // Set user context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      let query = supabase
        .from('load_planning')
        .select(`
          *,
          management_brick_types (
            name,
            category
          )
        `);

      // Apply date filtering if provided
      if (options?.dateFrom) {
        query = query.gte('date', format(options.dateFrom, 'yyyy-MM-dd'));
      }
      if (options?.dateTo) {
        query = query.lte('date', format(options.dateTo, 'yyyy-MM-dd'));
      }

      const { data, error } = await query.order('date', { ascending: true });

      if (error) throw error;
      return data as LoadPlanningEntry[];
    },
  });

  const markAsDispatchedMutation = useMutation({
    mutationFn: async (loadId: string) => {
      // Set user context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { error } = await supabase
        .from('load_planning')
        .update({
          dispatched: true,
          dispatched_at: new Date().toISOString(),
          dispatched_by: userId
        })
        .eq('id', loadId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['load-planning'] });
    },
    onError: (error) => {
      console.error('Error marking load as dispatched:', error);
    }
  });

  const markAsReadyMutation = useMutation({
    mutationFn: async (loadId: string) => {
      // Set user context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { error } = await supabase
        .from('load_planning')
        .update({
          ready: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', loadId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['load-planning'] });
    },
    onError: (error) => {
      console.error('Error marking load as ready:', error);
    }
  });

  const cancelLoadMutation = useMutation({
    mutationFn: async (loadId: string) => {
      // Set user context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { error } = await supabase
        .from('load_planning')
        .delete()
        .eq('id', loadId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['load-planning'] });
    },
    onError: (error) => {
      console.error('Error cancelling load:', error);
    }
  });

  const markAsDispached = (loadId: string) => {
    markAsDispatchedMutation.mutate(loadId);
  };

  const markAsReady = (loadId: string) => {
    markAsReadyMutation.mutate(loadId);
  };

  const cancelLoad = (loadId: string) => {
    cancelLoadMutation.mutate(loadId);
  };

  return {
    loads,
    isLoading,
    error,
    refetch,
    markAsDispached,
    markAsReady,
    cancelLoad,
    isMarkingDispatched: markAsDispatchedMutation.isPending,
    isMarkingReady: markAsReadyMutation.isPending,
    isCancelling: cancelLoadMutation.isPending
  };
};
