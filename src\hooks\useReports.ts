
import { useQuery } from '@tanstack/react-query';
import { getReportData, ReportData } from '@/data/reports';
import { TimeRange, CustomDateRange } from '@/components/dashboard/DashboardContent';
import { ReportType } from '@/components/pages/ReportsPage';
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

export function useReports(timeRange: TimeRange, reportType: ReportType, customDateRange?: CustomDateRange) {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery<ReportData, Error>({
    queryKey: ['reports', timeRange, reportType, customDateRange],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getReportData({ timeRange, reportType, customDateRange, userId: userId || undefined });
    },
    placeholderData: { main: [], secondary: [] },
  });
}
