
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TimeRange } from "./DashboardContent";
import { useQuery } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, eachDayOfInterval, eachWeekOfInterval } from "date-fns";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

interface ProductionBreakdownTableProps {
  timeRange: TimeRange;
}

export const ProductionBreakdownTable = ({ timeRange }: ProductionBreakdownTableProps) => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  const { data, isLoading } = useQuery({
    queryKey: ['production-breakdown', timeRange],
    queryFn: async () => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }
      const now = new Date();
      let dateRanges: { label: string; from: Date; to: Date }[] = [];

      if (timeRange === 'today') {
        dateRanges = [{ label: format(now, 'EEEE'), from: startOfDay(now), to: endOfDay(now) }];
      } else if (timeRange === 'week') {
        const weekStart = startOfWeek(now, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(now, { weekStartsOn: 1 });
        const days = eachDayOfInterval({ start: weekStart, end: weekEnd });
        dateRanges = days.map(day => ({
          label: format(day, 'EEEE'),
          from: startOfDay(day),
          to: endOfDay(day)
        }));
      } else if (timeRange === 'month') {
        const monthStart = startOfMonth(now);
        const monthEnd = endOfMonth(now);
        const weeks = eachWeekOfInterval({ start: monthStart, end: monthEnd }, { weekStartsOn: 1 });
        dateRanges = weeks.map((week, index) => ({
          label: `Week ${index + 1}`,
          from: week,
          to: endOfWeek(week, { weekStartsOn: 1 })
        }));
      }

      const results = await Promise.all(dateRanges.map(async ({ label, from, to }) => {
        const fromStr = format(from, 'yyyy-MM-dd');
        const toStr = format(to, 'yyyy-MM-dd');

        const [factoryResult, settingResult, dehackingResult, breakdownsResult] = await Promise.all([
          supabase.from('production_entries').select('pallet_count').gte('date', fromStr).lte('date', toStr),
          supabase.from('setting_production_entries').select('pallet_count').gte('date', fromStr).lte('date', toStr),
          supabase.from('dehacking_entries').select('pallet_count').gte('date', fromStr).lte('date', toStr),
          supabase.from('breakdowns').select('start_time, stop_time, date').gte('date', fromStr).lte('date', toStr)
        ]);

        const factoryProduction = factoryResult.data?.reduce((sum, entry) => sum + entry.pallet_count, 0) || 0;
        const settingProduction = settingResult.data?.reduce((sum, entry) => sum + entry.pallet_count, 0) || 0;
        const dehackingProduction = dehackingResult.data?.reduce((sum, entry) => sum + entry.pallet_count, 0) || 0;

        // Calculate downtime hours
        const downtimeHours = breakdownsResult.data?.reduce((total, breakdown) => {
          if (breakdown.start_time && breakdown.start_time.trim() !== "" && breakdown.stop_time) {
            const start = new Date(`${breakdown.date}T${breakdown.start_time}`);
            const stop = new Date(`${breakdown.date}T${breakdown.stop_time}`);
            const hours = Math.abs(stop.getTime() - start.getTime()) / (1000 * 60 * 60);
            return total + hours;
          }
          return total;
        }, 0) || 0;

        return {
          period: label,
          factoryProduction,
          settingProduction,
          dehackingProduction,
          downtimeHours: Math.round(downtimeHours * 100) / 100
        };
      }));

      return results;
    }
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Production Breakdown</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-40">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Production Breakdown ({timeRange})</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Period</TableHead>
              <TableHead>Factory Production</TableHead>
              <TableHead>Setting Records</TableHead>
              <TableHead>Dehacking Records</TableHead>
              <TableHead>Downtime Hours</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.map((row) => (
              <TableRow key={row.period}>
                <TableCell className="font-medium">{row.period}</TableCell>
                <TableCell>{row.factoryProduction.toLocaleString()}</TableCell>
                <TableCell>{row.settingProduction.toLocaleString()}</TableCell>
                <TableCell>{row.dehackingProduction.toLocaleString()}</TableCell>
                <TableCell>{row.downtimeHours}h</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
