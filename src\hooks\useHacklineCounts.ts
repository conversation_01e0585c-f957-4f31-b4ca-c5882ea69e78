
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { getManagementBrickTypes } from "@/data/managementBrickTypes";

export interface HacklineCount {
  id: number;
  created_at: string;
  date: string;
  count_total: number;
  user_id: string;
  notes?: string;
  pallet_type: 'Imperial' | 'Maxi';
  pallet_count: number;
}

export interface NewHacklineCount {
  date: string;
  pallet_count: number;
  pallet_type: 'Imperial' | 'Maxi';
  notes?: string;
}

export function useHacklineCounts() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["hacklineCounts"],
    queryFn: async () => {
      try {
        // Get effective user ID and set context
        const userId = getEffectiveUserId(currentUser, userContextUser);
        if (userId) {
          await setUserContext(userId);
        }

        const { data, error } = await supabase
          .from("hackline_counts")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) {
          console.error("Error fetching hackline counts:", error);

          // If table doesn't exist, try to get data from localStorage
          if (error.code === '42P01' || error.message.includes('relation "hackline_counts" does not exist')) {
            console.log("Table doesn't exist, loading from localStorage");
            const fallbackData = JSON.parse(localStorage.getItem('hackline_counts') || '[]');
            return fallbackData as HacklineCount[];
          }

          throw new Error(error.message);
        }

        return (data || []) as HacklineCount[];
      } catch (error) {
        console.error("Error in hackline counts query:", error);

        // Final fallback to localStorage
        try {
          const fallbackData = JSON.parse(localStorage.getItem('hackline_counts') || '[]');
          console.log("Using localStorage fallback data:", fallbackData);
          return fallbackData as HacklineCount[];
        } catch (localStorageError) {
          console.error("Error reading from localStorage:", localStorageError);
          return [];
        }
      }
    },
    retry: false, // Don't retry if table doesn't exist
  });
}

export function useAddHacklineCount() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (newCount: NewHacklineCount) => {
      console.log("🚀 Starting hackline count mutation with data:", newCount);

      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        console.error("❌ User not authenticated");
        throw new Error("User not authenticated");
      }

      console.log("👤 Using user ID:", userId);
      await setUserContext(userId);

      // Get the correct bricks per pallet from management_brick_types
      let count_total: number;
      try {
        const brickTypes = await getManagementBrickTypes();
        const targetBrickType = newCount.pallet_type === 'Imperial' 
          ? brickTypes.find(bt => bt.id === 'imperial_extruded')
          : brickTypes.find(bt => bt.id === 'maxi_extruded');
        
        if (targetBrickType) {
          count_total = newCount.pallet_count * targetBrickType.bricks_per_pallet;
          console.log(`📊 Using ${targetBrickType.bricks_per_pallet} bricks per pallet for ${newCount.pallet_type}`);
        } else {
          // Fallback to old calculation if brick types not found
          count_total = newCount.pallet_type === 'Imperial' 
            ? newCount.pallet_count * 320 
            : newCount.pallet_count * 280;
          console.log("⚠️ Using fallback calculation");
        }
      } catch (error) {
        console.error("Error fetching brick types, using fallback:", error);
        // Fallback calculation
        count_total = newCount.pallet_type === 'Imperial' 
          ? newCount.pallet_count * 320 
          : newCount.pallet_count * 280;
      }

      try {
        console.log("📝 Attempting database insert...");
        // Try to insert into database first
        const { data, error } = await supabase
          .from("hackline_counts")
          .insert({
            date: newCount.date,
            pallet_count: newCount.pallet_count,
            pallet_type: newCount.pallet_type,
            count_total: count_total,
            notes: newCount.notes,
            user_id: userId,
          })
          .select()
          .single();

        if (error) {
          console.error("❌ Database error:", error);
          console.error("❌ Error code:", error.code);
          console.error("❌ Error message:", error.message);

          // Always use localStorage fallback for now
          console.log("💾 Using localStorage fallback...");

          const fallbackData: HacklineCount = {
            id: Date.now(), // Use timestamp as ID
            created_at: new Date().toISOString(),
            date: newCount.date,
            pallet_count: newCount.pallet_count,
            pallet_type: newCount.pallet_type,
            count_total: count_total,
            notes: newCount.notes || null,
            user_id: userId,
          };

          // Store in localStorage
          const existingData = JSON.parse(localStorage.getItem('hackline_counts') || '[]');
          existingData.unshift(fallbackData);
          localStorage.setItem('hackline_counts', JSON.stringify(existingData));

          console.log("✅ Stored in localStorage:", fallbackData);
          return fallbackData;
        }

        console.log("✅ Database insert successful:", data);
        return data as HacklineCount;
      } catch (err) {
        console.error("💥 Unexpected error:", err);

        // Final fallback - always try localStorage
        console.log("🔄 Final fallback to localStorage...");
        try {
          const fallbackData: HacklineCount = {
            id: Date.now(), // Use timestamp as ID
            created_at: new Date().toISOString(),
            date: newCount.date,
            pallet_count: newCount.pallet_count,
            pallet_type: newCount.pallet_type,
            count_total: count_total,
            notes: newCount.notes || null,
            user_id: currentUser.id,
          };

          const existingData = JSON.parse(localStorage.getItem('hackline_counts') || '[]');
          existingData.unshift(fallbackData);
          localStorage.setItem('hackline_counts', JSON.stringify(existingData));

          console.log("✅ Final fallback successful:", fallbackData);
          return fallbackData;
        } catch (localStorageError) {
          console.error("💥 Even localStorage failed:", localStorageError);
          throw new Error("Failed to save hackline count");
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["hacklineCounts"] });
    },
  });
}
