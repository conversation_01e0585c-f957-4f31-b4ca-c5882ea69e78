
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { getTeamMembers, getTeamsWithMembers } from "@/data/teamData";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface StrappingDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const STRAPPING_RATE = 1.50;

export const StrappingDialog = ({ isOpen, onClose }: StrappingDialogProps) => {
  const [selectedTeam, setSelectedTeam] = useState<string>("");
  const [selectedEmployee, setSelectedEmployee] = useState<string>("");
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [palletCount, setPalletCount] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get all teams from the database
  const { data: allTeams = [] } = useQuery({
    queryKey: ['teamsWithMembers'],
    queryFn: () => getTeamsWithMembers()
  });

  // Get team members for the selected team
  const { data: teamMembers = [] } = useQuery({
    queryKey: ['team-members', selectedTeam],
    queryFn: () => getTeamMembers(selectedTeam),
    enabled: !!selectedTeam
  });

  const calculatePay = () => {
    const count = parseInt(palletCount) || 0;
    return count * STRAPPING_RATE;
  };

  const handleSubmit = async () => {
    if (!selectedTeam || !selectedEmployee || !date || !palletCount) {
      toast.error("Please fill in all fields");
      return;
    }

    const count = parseInt(palletCount);
    if (count <= 0) {
      toast.error("Pallet count must be greater than 0");
      return;
    }

    const pay = calculatePay();
    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('strapping_records')
        .insert({
          employee_id: parseInt(selectedEmployee),
          team_id: selectedTeam,
          date: date,
          pallet_count: count,
          rate_per_pallet: STRAPPING_RATE,
          total_pay: pay
        });

      if (error) {
        console.error('Error saving strapping record:', error);
        toast.error("Failed to save strapping record");
        return;
      }

      toast.success(`Recorded ${count} pallets for R${pay.toFixed(2)}`);
      
      // Reset form
      setSelectedTeam("");
      setSelectedEmployee("");
      setDate(new Date().toISOString().split('T')[0]);
      setPalletCount("");
      onClose();
    } catch (error) {
      console.error('Error saving strapping record:', error);
      toast.error("Failed to save strapping record");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Record Strapping</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="date">Date</Label>
            <Input
              id="date"
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="team">Team</Label>
            <Select value={selectedTeam} onValueChange={(value) => {
              setSelectedTeam(value);
              setSelectedEmployee(""); // Reset employee when team changes
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Select team" />
              </SelectTrigger>
              <SelectContent>
                {allTeams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="employee">Employee</Label>
            <Select value={selectedEmployee} onValueChange={setSelectedEmployee} disabled={!selectedTeam}>
              <SelectTrigger>
                <SelectValue placeholder="Select employee" />
              </SelectTrigger>
              <SelectContent>
                {teamMembers.map((member) => (
                  <SelectItem key={member.id} value={member.id.toString()}>
                    {member.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="palletCount">Pallets Strapped</Label>
            <Input
              id="palletCount"
              type="number"
              min="1"
              value={palletCount}
              onChange={(e) => setPalletCount(e.target.value)}
              placeholder="Enter number of pallets"
            />
          </div>

          {palletCount && parseInt(palletCount) > 0 && (
            <div className="bg-gray-50 p-3 rounded-md">
              <div className="text-sm text-gray-600">
                Rate: R{STRAPPING_RATE}/pallet
              </div>
              <div className="text-sm font-medium">
                Pay: R{calculatePay().toFixed(2)}
              </div>
            </div>
          )}

          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1" disabled={isSubmitting}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} className="flex-1" disabled={isSubmitting}>
              {isSubmitting ? "Recording..." : "Record Strapping"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
