
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://hhxwnoreclckmtenugmt.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoeHdub3JlY2xja210ZW51Z210Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTA2ODksImV4cCI6MjA2NTQ4NjY4OX0.Sx0m7H7YgSbOANJQ2YhlrZqsNms9J757ttQlQnOr-ek";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Regular client for general operations (service role key removed for security)
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Set user context for RLS policies
export const setUserContext = async (userId: string) => {
  try {
    console.log('Setting user context:', userId);
    const result = await supabase.rpc('set_config', {
      setting_name: 'app.current_user_id',
      setting_value: userId,
      is_local: false
    });
    console.log('User context set result:', result);
    return result;
  } catch (error) {
    console.error('Failed to set user context:', error);
    throw error;
  }
};

// Clear user context
export const clearUserContext = async () => {
  try {
    await supabase.rpc('set_config', {
      setting_name: 'app.current_user_id',
      setting_value: '',
      is_local: false
    });
  } catch (error) {
    console.error('Failed to clear user context:', error);
  }
};

// Create a Supabase client with user context set
export const createAuthenticatedSupabaseClient = async (userId: string) => {
  await setUserContext(userId);
  return supabase;
};
