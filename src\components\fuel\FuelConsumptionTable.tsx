
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Loader2 } from "lucide-react";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { calculateAutomaticFuelUsage } from "@/utils/fuelUsageCalculations";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

interface FuelConsumptionData {
  assetId: string;
  assetName: string;
  assetType: string;
  lastFill: string | null;
  newFill: string | null;
  hoursOrKm: string;
  previousHoursOrKm: number;
  averageConsumption: string;
}

export const FuelConsumptionTable = () => {
  const [consumptionData, setConsumptionData] = useState<FuelConsumptionData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  useEffect(() => {
    fetchConsumptionData();
  }, []);

  const fetchConsumptionData = async () => {
    try {
      setIsLoading(true);

      // Set user context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      // Get all assets
      const { data: assets, error: assetsError } = await supabase
        .from('assets')
        .select('id, name, type')
        .eq('status', 'active');

      if (assetsError) throw assetsError;

      const consumptionPromises = assets.map(async (asset) => {
        // Get fuel dispensing transactions for this asset
        const { data: transactions, error: transError } = await supabase
          .from('fuel_dispensing_transactions')
          .select('*')
          .eq('asset_id', asset.id)
          .order('transaction_date', { ascending: false })
          .order('created_at', { ascending: false });

        if (transError) throw transError;

        const lastTransaction = transactions[0];
        const secondLastTransaction = transactions[1];
        
        let lastFill = null;
        let newFill = null;
        let hoursOrKm = "No data";
        let previousHoursOrKm = 0;
        let averageConsumption = "No data";

        if (lastTransaction) {
          lastFill = lastTransaction.transaction_date;
          if (lastTransaction.starting_reading) {
            newFill = lastTransaction.starting_reading.toString();
          }

          // Calculate hours/km from readings (current - previous)
          if (lastTransaction.starting_reading && secondLastTransaction?.starting_reading) {
            const readingDiff = lastTransaction.starting_reading - secondLastTransaction.starting_reading;
            hoursOrKm = readingDiff.toString();
          }

          // Calculate previous hours/km (from the second last transaction)
          if (secondLastTransaction?.starting_reading && transactions[2]?.starting_reading) {
            previousHoursOrKm = secondLastTransaction.starting_reading - transactions[2].starting_reading;
          } else if (secondLastTransaction?.starting_reading) {
            // If there's only one previous transaction, use that reading as the previous hours/km
            previousHoursOrKm = secondLastTransaction.starting_reading;
          }

          // Calculate total hours/km for average consumption calculation
          let totalHoursOrKm = 0;
          for (let i = 0; i < transactions.length - 1; i++) {
            const current = transactions[i];
            const previous = transactions[i + 1];
            if (current.starting_reading && previous.starting_reading) {
              totalHoursOrKm += current.starting_reading - previous.starting_reading;
            }
          }

          // Calculate average consumption
          const totalFuelUsed = transactions.reduce((sum, t) => sum + (t.quantity_liters || 0), 0);
          if (totalFuelUsed > 0 && totalHoursOrKm > 0) {
            const avgConsumption = totalFuelUsed / totalHoursOrKm;
            averageConsumption = `${avgConsumption.toFixed(2)} L/hr`;

            // If it's a vehicle, show km/L instead
            if (asset.type.toLowerCase().includes('truck') || asset.type.toLowerCase().includes('vehicle')) {
              const kmPerLiter = totalHoursOrKm / totalFuelUsed;
              averageConsumption = `${kmPerLiter.toFixed(2)} km/L`;
            }
          }
        }

        return {
          assetId: asset.id,
          assetName: asset.name,
          assetType: asset.type,
          lastFill,
          newFill,
          hoursOrKm,
          previousHoursOrKm,
          averageConsumption
        };
      });

      const results = await Promise.all(consumptionPromises);
      setConsumptionData(results);
    } catch (error) {
      console.error('Error fetching consumption data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Fuel Consumption Tracking</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-slate-500" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fuel Consumption Tracking</CardTitle>
        <p className="text-sm text-slate-600">Asset fuel usage and consumption data</p>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Asset</TableHead>
              <TableHead>Last Fill Date</TableHead>
              <TableHead>New Fill Reading</TableHead>
              <TableHead>Hours/KM Recorded</TableHead>
              <TableHead>Previous Hours/KM</TableHead>
              <TableHead>Average Consumption</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {consumptionData.map((item) => (
              <TableRow key={item.assetId}>
                <TableCell>
                  <div>
                    <div className="font-medium">{item.assetName}</div>
                    <div className="text-sm text-slate-500">{item.assetType}</div>
                  </div>
                </TableCell>
                <TableCell>{item.lastFill || "No data"}</TableCell>
                <TableCell>{item.newFill || "No data"}</TableCell>
                <TableCell>{item.hoursOrKm}</TableCell>
                <TableCell>{item.previousHoursOrKm.toLocaleString()}</TableCell>
                <TableCell>{item.averageConsumption}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
