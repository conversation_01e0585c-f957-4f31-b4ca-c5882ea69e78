
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON> } from 'recharts';
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { format, subDays } from 'date-fns';
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

const KILN_OPTIONS = [
  { id: "habla-kiln", name: "Habla Kiln" },
  { id: "kiln-1", name: "Kiln 1" },
  { id: "kiln-2", name: "Kiln 2" },
  { id: "kiln-3", name: "Kiln 3" },
  { id: "kiln-4", name: "Kiln 4" },
  { id: "kiln-5", name: "Kiln 5" },
];

const PARAMETER_TESTS = [
  'Pre-heat Zone 1 Temp',
  'Pre-heat Zone 2 Temp',
  'Pre-combustion Zone Temp',
  'Fire Zone Temp',
  'Cooling Zone 1 Temp',
  'Cooling Zone 2 Temp',
  'Fire Position in Chamber',
  'Fire Movement',
  'O2',
  'CO2',
  'Draught Pressure',
  'Fuel to Brick Ratio - Setting',
  'Brick Moisture % - Setting'
];

const COLORS = [
  '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
  '#ec4899', '#06b6d4', '#84cc16', '#f97316'
];

export const ParameterTrends = () => {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();
  const [selectedKiln, setSelectedKiln] = useState("habla-kiln");
  const endDate = new Date();
  const startDate = subDays(endDate, 7);

  const { data: trendsData = [], isLoading } = useQuery({
    queryKey: ['parameter-trends', selectedKiln, startDate, endDate],
    queryFn: async () => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      console.log('🔍 Fetching parameter trends for kiln:', selectedKiln, 'from', format(startDate, 'yyyy-MM-dd'), 'to', format(endDate, 'yyyy-MM-dd'));

      const { data, error } = await supabase
        .from('kiln_monitoring_measurements')
        .select('*')
        .eq('kiln_id', selectedKiln)
        .gte('measurement_date', format(startDate, 'yyyy-MM-dd'))
        .lte('measurement_date', format(endDate, 'yyyy-MM-dd'))
        .order('measurement_date')
        .order('measurement_time');

      if (error) {
        console.error('Error fetching trends data:', error);
        return [];
      }

      console.log('📊 Parameter trends fetched:', data?.length || 0, 'measurements');
      console.log('📊 Sample trends measurement:', data?.[0]);

      // Transform data for chart
      const chartData = data.map(measurement => {
        const timestamp = `${measurement.measurement_date} ${measurement.measurement_time}`;
        const result: any = { timestamp };
        
        PARAMETER_TESTS.forEach(test => {
          const paramValue = (measurement.parameters as Record<string, number>)[test];
          if (paramValue !== undefined) {
            result[test] = paramValue;
          }
        });

        return result;
      });

      return chartData;
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
    refetchInterval: 60000, // Refetch every 60 seconds for trends (less frequent than real-time data)
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Parameter Trends (Last 7 Days)</CardTitle>
        <div className="flex items-center gap-4">
          <Select value={selectedKiln} onValueChange={setSelectedKiln}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {KILN_OPTIONS.map((kiln) => (
                <SelectItem key={kiln.id} value={kiln.id}>
                  {kiln.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-96 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={trendsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis />
              <Tooltip />
              <Legend />
              {PARAMETER_TESTS.map((test, index) => (
                <Line
                  key={test}
                  type="monotone"
                  dataKey={test}
                  stroke={COLORS[index % COLORS.length]}
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  connectNulls={false}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
