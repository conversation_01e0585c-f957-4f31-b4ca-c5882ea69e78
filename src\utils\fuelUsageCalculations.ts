import { supabase, setUserContext } from "@/integrations/supabase/client";

// Asset type detection based on asset type field and name
export function detectAssetReadingType(assetType: string, assetName: string): "hourly" | "km" {
  const lower = assetName.toLowerCase();
  const typeLower = assetType.toLowerCase();
  
  // Check asset type first
  if (typeLower.includes("truck") || typeLower.includes("vehicle")) return "km";
  if (typeLower.includes("generator") || typeLower.includes("machinery")) return "hourly";
  
  // Fallback to name-based detection
  if (lower.includes("truck") || lower.includes("vehicle") || lower.includes("car")) return "km";
  if (lower.includes("kiln") || lower.includes("generator") || lower.includes("forklift") || lower.includes("machinery")) return "hourly";
  
  return "hourly"; // Default fallback
}

// Get the last reading for an asset
export async function getLastAssetReading(assetId: string): Promise<number | null> {
  try {
    const { data, error } = await supabase
      .from('fuel_dispensing_transactions')
      .select('starting_reading')
      .eq('asset_id', assetId)
      .not('starting_reading', 'is', null)
      .order('transaction_date', { ascending: false })
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error fetching last reading:', error);
      return null;
    }

    return data && data.length > 0 ? data[0].starting_reading : null;
  } catch (error) {
    console.error('Error in getLastAssetReading:', error);
    return null;
  }
}

// Calculate fuel usage efficiency
export function calculateFuelUsage(
  lastReading: number,
  currentReading: number,
  litresFilled: number,
  readingType: "hourly" | "km"
): { usage: number; unit: string } | null {
  const difference = currentReading - lastReading;
  
  if (difference <= 0 || litresFilled <= 0) {
    return null;
  }

  if (readingType === "hourly") {
    // Litres per hour
    const litresPerHour = litresFilled / difference;
    return {
      usage: litresPerHour,
      unit: "L/hr"
    };
  } else {
    // Kilometres per litre
    const kmPerLitre = difference / litresFilled;
    return {
      usage: kmPerLitre,
      unit: "km/L"
    };
  }
}

// Get asset information including type
export async function getAssetInfo(assetId: string, userId?: string): Promise<{ name: string; type: string } | null> {
  try {
    if (userId) {
      await setUserContext(userId);
    }

    const { data, error } = await supabase
      .from('assets')
      .select('name, type')
      .eq('id', assetId)
      .single();

    if (error) {
      console.error('Error fetching asset info:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getAssetInfo:', error);
    return null;
  }
}

// Main function to calculate automatic fuel usage
export async function calculateAutomaticFuelUsage(
  assetId: string,
  currentReading: number,
  litresFilled: number
): Promise<{
  lastReading: number | null;
  currentReading: number;
  usage: { usage: number; unit: string } | null;
  readingType: "hourly" | "km";
  assetInfo: { name: string; type: string } | null;
}> {
  // Get asset information
  const assetInfo = await getAssetInfo(assetId);
  
  if (!assetInfo) {
    return {
      lastReading: null,
      currentReading,
      usage: null,
      readingType: "hourly",
      assetInfo: null
    };
  }

  // Determine reading type
  const readingType = detectAssetReadingType(assetInfo.type, assetInfo.name);

  // Get last reading
  const lastReading = await getLastAssetReading(assetId);

  // Calculate usage if we have a last reading
  let usage = null;
  if (lastReading !== null) {
    usage = calculateFuelUsage(lastReading, currentReading, litresFilled, readingType);
  }

  return {
    lastReading,
    currentReading,
    usage,
    readingType,
    assetInfo
  };
}
