
import { supabase, setUserContext } from "@/integrations/supabase/client";

export async function addProductionEntry(entry: { date: string; brick_type_id: string; pallet_count: number; }, userId?: string): Promise<void> {
  if (userId) {
    await setUserContext(userId);
  }

  const { error } = await supabase.from("production_entries").insert(entry);
  if (error) {
    console.error("Error adding production entry:", error);
    throw error;
  }
}
