
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { toast } from "sonner";

export interface Breakdown {
  id: number;
  created_at: string;
  date: string;
  breakdown_comment: string;
  time: string;
  stop_time: string;
  start_time: string | null;
  user_id: string;
  status?: 'Unresolved' | 'Resolved'; // Computed status
}

export interface NewBreakdown {
  date: string;
  breakdown_comment: string;
  stop_time: string;
  start_time?: string | null;
}

export interface UpdateBreakdown {
  id: number;
  date: string;
  breakdown_comment: string;
  stop_time: string;
  start_time?: string | null;
}

export function useBreakdowns() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["breakdowns"],
    queryFn: async () => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (userId) {
        await setUserContext(userId);
      }

      const { data, error } = await supabase
        .from("breakdowns")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching breakdowns:", error);
        throw new Error(error.message);
      }

      // Add computed status based on start_time
      const breakdownsWithStatus = (data || []).map((breakdown): Breakdown => ({
        ...breakdown,
        status: breakdown.start_time ? 'Resolved' : 'Unresolved'
      }));

      return breakdownsWithStatus;
    },
  });
}

export function useAddBreakdown() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (newBreakdown: NewBreakdown) => {
      console.log("🔄 Recording breakdown:", newBreakdown);

      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        console.error("❌ User not authenticated");
        throw new Error("User not authenticated");
      }

      console.log("👤 Using user ID:", userId);
      await setUserContext(userId);

      // Prepare the data for insertion - ensure start_time is null when not provided
      const insertData = {
        date: newBreakdown.date,
        breakdown_comment: newBreakdown.breakdown_comment,
        time: newBreakdown.stop_time,
        stop_time: newBreakdown.stop_time,
        start_time: newBreakdown.start_time || null,
        user_id: userId,
      };

      console.log("📝 Insert data:", insertData);

      const { data, error } = await supabase
        .from("breakdowns")
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error("❌ Database error:", error);
        console.error("❌ Error details:", {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw new Error(`Database error: ${error.message}`);
      }

      console.log("✅ Breakdown recorded successfully:", data);
      
      // Add computed status
      const breakdownWithStatus: Breakdown = {
        ...data,
        status: data.start_time ? 'Resolved' : 'Unresolved'
      };
      
      return breakdownWithStatus;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["breakdowns"] });
    },
    onError: (error) => {
      console.error("❌ Mutation error:", error);
    }
  });
}

export function useUpdateBreakdown() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (updateBreakdown: UpdateBreakdown) => {
      console.log("🔄 Updating breakdown:", updateBreakdown);

      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        console.error("❌ User not authenticated");
        throw new Error("User not authenticated");
      }

      console.log("👤 Using user ID:", userId);
      await setUserContext(userId);

      const updateData = {
        date: updateBreakdown.date,
        breakdown_comment: updateBreakdown.breakdown_comment,
        time: updateBreakdown.stop_time,
        stop_time: updateBreakdown.stop_time,
        start_time: updateBreakdown.start_time || null,
      };

      console.log("📝 Update data:", updateData);

      const { data, error } = await supabase
        .from("breakdowns")
        .update(updateData)
        .eq('id', updateBreakdown.id)
        .select()
        .single();

      if (error) {
        console.error("❌ Database error:", error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log("✅ Breakdown updated successfully:", data);

      // Add computed status
      const breakdownWithStatus: Breakdown = {
        ...data,
        status: data.start_time ? 'Resolved' : 'Unresolved'
      };

      return breakdownWithStatus;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["breakdowns"] });
    },
    onError: (error) => {
      console.error("❌ Update mutation error:", error);
    }
  });
}

export function useDeleteBreakdown() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (breakdownId: number) => {
      // Get effective user ID and set context
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      await setUserContext(userId);

      const { error } = await supabase
        .from("breakdowns")
        .delete()
        .eq('id', breakdownId);

      if (error) {
        console.error("Error deleting breakdown:", error);
        throw new Error(error.message);
      }

      return breakdownId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["breakdowns"] });
    },
  });
}
