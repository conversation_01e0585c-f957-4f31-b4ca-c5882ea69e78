import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface ChamberFireStatus {
  id: number;
  kiln_id: string;
  chamber_number: number;
  is_burning: boolean;
  updated_by: string | null;
  created_at: string;
  updated_at: string;
}

export function useChamberFireStatus() {
  return useQuery({
    queryKey: ["chamberFireStatus"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("chamber_fire_status")
          .select("*")
          .order("updated_at", { ascending: false });

        if (error) {
          console.error("Error fetching chamber fire status:", error);
          throw new Error(error.message);
        }

        return (data || []) as ChamberFireStatus[];
      } catch (error) {
        console.error("Error in chamber fire status query:", error);
        return [];
      }
    },
    retry: false,
  });
}

export function useUpdateChamberFireStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (update: { id: number; is_burning: boolean; updated_by?: string | null }) => {
      try {
        const { data, error } = await supabase
          .from("chamber_fire_status")
          .update({
            is_burning: update.is_burning,
            updated_by: update.updated_by || null,
            updated_at: new Date().toISOString()
          })
          .eq("id", update.id)
          .select()
          .single();

        if (error) {
          console.error("Error updating chamber fire status:", error);
          throw new Error(error.message);
        }

        return data as ChamberFireStatus;
      } catch (error) {
        console.error("Error in chamber fire status update:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["chamberFireStatus"] });
    },
  });
}
