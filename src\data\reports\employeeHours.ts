
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { calculateGrossEarningsForPeriod } from "../earningsCalculations";
import type { ReportData } from "./types";

export const getEmployeeHoursReport = async ({ from, to }: { from: string, to: string }, userId?: string): Promise<ReportData> => {
  if (userId) {
    await setUserContext(userId);
  }

  const { data: employees, error: employeesError } = await supabase.from('employees').select('*');
  if (employeesError) throw employeesError;

  const earnings = await calculateGrossEarningsForPeriod({ from, to }, userId);
  
  const byDepartment = (employees || []).reduce((acc, emp) => {
    if (!emp.department) return acc;
    if (!acc[emp.department]) {
      acc[emp.department] = {
        name: emp.department,
        setting_earnings: 0,
        dehacking_earnings: 0,
        total_earnings: 0,
        employees: 0,
      };
    }
    acc[emp.department].employees += 1;
    return acc;
  }, {} as Record<string, { name: string, setting_earnings: number, dehacking_earnings: number, total_earnings: number, employees: number }>);

  earnings.forEach(earning => {
    const emp = (employees || []).find(e => e.id === earning.employeeId);
    if (emp && emp.department && byDepartment[emp.department]) {
      byDepartment[emp.department].setting_earnings += earning.settingTotal;
      byDepartment[emp.department].dehacking_earnings += earning.dehackingTotal;
      byDepartment[emp.department].total_earnings += earning.total;
    }
  });

  const main = Object.values(byDepartment);
  const secondary = earnings
    .filter(e => e.total > 0)
    .map(e => ({
      employee: e.employeeName,
      employee_code: (employees || []).find(emp => emp.id === e.employeeId)?.employee_code || 'N/A',
      department: (employees || []).find(emp => emp.id === e.employeeId)?.department || 'N/A',
      earnings: e.total,
  }));
  
  return { main, secondary };
};
