
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useAIInsights } from '@/hooks/useAIInsights';
import { TrendingUp, TrendingDown, AlertTriangle, CheckCircle, Clock, Target } from 'lucide-react';

export const AIOverviewDashboard = () => {
  const { data: insights, isLoading } = useAIInsights('overview');

  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-blue-500" />;
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <Target className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-3">
        {insights?.keyMetrics?.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
              {getTrendIcon(metric.trend)}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{metric.change}</span>
                <span>{metric.description}</span>
              </div>
            </CardContent>
          </Card>
        )) || (
          <Card className="col-span-3">
            <CardContent className="p-6">
              <p className="text-muted-foreground">No production data available for today. Add some production entries to see insights.</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Alerts and Recommendations */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              AI Alerts
            </CardTitle>
            <CardDescription>
              Automated insights based on data analysis
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {insights?.alerts?.length > 0 ? insights.alerts.map((alert, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                {getAlertIcon(alert.type)}
                <div className="flex-1 space-y-1">
                  <div className="flex items-center gap-2">
                    <h4 className="text-sm font-semibold">{alert.title}</h4>
                    <Badge variant={alert.priority === 'high' ? 'destructive' : 'secondary'} className="text-xs">
                      {alert.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{alert.description}</p>
                </div>
              </div>
            )) : (
              <p className="text-muted-foreground text-sm">No alerts at this time. System is running smoothly.</p>
            )}
          </CardContent>
        </Card>

        {/* Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              AI Recommendations
            </CardTitle>
            <CardDescription>
              Actionable insights for business improvement
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {insights?.recommendations?.length > 0 ? insights.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-xs font-medium text-primary">
                    {index + 1}
                  </div>
                  <p className="text-sm">{recommendation}</p>
                </div>
              )) : (
                <p className="text-muted-foreground text-sm">Generating recommendations based on your production data...</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
