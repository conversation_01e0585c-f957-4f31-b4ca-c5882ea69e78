
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Flame, Loader2 } from "lucide-react";
import { TimeRange } from "../DashboardContent";
import { getSettingSummary } from "@/data/settingProductionStore";
import { isToday, isThisWeek, isThisMonth, startOfYear } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { getFires } from "@/data/kilnData";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";

interface SettingSummaryCardProps {
  timeRange: TimeRange;
}

export const SettingSummaryCard = ({ timeRange }: SettingSummaryCardProps) => {
  const { data: allFiresData = [], isLoading: isLoadingFires } = useQuery({
    queryKey: ['fires'],
    queryFn: () => getFires(),
  });

  const { data: brickTypes = [], isLoading: isLoadingBrickTypes } = useQuery<ManagementBrickType[]>({
    queryKey: ['managementBrickTypes'],
    queryFn: () => getManagementBrickTypes(),
  });

  const timeFilter = (date: Date): boolean => {
    switch (timeRange) {
      case "today":
        return isToday(date);
      case "week":
        return isThisWeek(date, { weekStartsOn: 1 });
      case "month":
        return isThisMonth(date);
      case "year":
        return date >= startOfYear(new Date());
      default:
        return true;
    }
  };
  
  const { data: summary = [], isLoading: isLoadingSummary } = useQuery({
    queryKey: ['settingSummary', timeRange, allFiresData, brickTypes],
    queryFn: () => getSettingSummary(timeFilter, allFiresData, brickTypes),
    enabled: !isLoadingFires && !isLoadingBrickTypes && allFiresData.length > 0 && brickTypes.length > 0,
  });
  
  const totalBricks = summary.reduce((acc, fire) => acc + fire.bricks, 0);

  const periodText = {
      today: "Today",
      week: "This Week",
      month: "This Month",
      year: "This Year"
  }[timeRange];

  const isLoading = isLoadingFires || isLoadingBrickTypes || isLoadingSummary;

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-slate-800 flex items-center gap-2">
          <Flame size={20} />
          Setting Summary ({periodText})
        </CardTitle>
        <p className="text-sm text-slate-600">Total bricks set per fire.</p>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-slate-500" />
          </div>
        ) : summary.length > 0 ? (
          <div className="space-y-3">
            <div className="flex justify-between items-baseline">
                <p className="font-bold text-slate-800">Total Bricks Set:</p>
                <p className="text-2xl font-bold text-slate-800">{totalBricks.toLocaleString()}</p>
            </div>
            <div className="border-t pt-3 space-y-2">
              {summary.map(fire => (
                <div key={fire.fireId} className="flex justify-between text-sm">
                  <span className="text-slate-600">{fire.fireName}</span>
                  <span className="font-medium text-slate-700">{fire.bricks.toLocaleString()} bricks ({fire.pallets} pallets)</span>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-slate-500">
            No setting production recorded for this period.
          </div>
        )}
      </CardContent>
    </Card>
  );
};
