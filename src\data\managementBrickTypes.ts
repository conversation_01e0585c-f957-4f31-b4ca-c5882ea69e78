
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { getEffectiveUserId } from "@/hooks/useUsers";
export interface ManagementBrickType {
  id: string;
  name: string;
  category: string;
  grade: string;
  setting_rate: number;
  dehacking_rate: number;
  overtime_rate: number;
  status: string;
  bricks_per_pallet: number;
  dehacking_day_rate?: number;
  dehacking_night_rate?: number;
  setting_day_rate?: number;
  setting_night_rate?: number;
  brick_stage?: string; // "extruded" | "finished"
}

export const getManagementBrickTypes = async (userId?: string): Promise<ManagementBrickType[]> => {
  // Set user context if provided
  if (userId) {
    await setUserContext(userId);
  }

  const { data, error } = await supabase.from('management_brick_types').select('*');
  if (error) {
    console.error("Error fetching management brick types", error);
    throw new Error(error.message);
  }
  return data as ManagementBrickType[];
};

export const getExtrudedBrickTypes = async (userId?: string): Promise<ManagementBrickType[]> => {
  const all = await getManagementBrickTypes(userId);
  return all.filter(bt => bt.brick_stage === "extruded");
};

export const getFinishedBrickTypes = async (userId?: string): Promise<ManagementBrickType[]> => {
  const all = await getManagementBrickTypes(userId);
  return all.filter(bt => bt.brick_stage === "finished");
};
