
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { format } from 'date-fns';
import type { ReportData } from "./types";

export const getFactoryOutputReport = async ({ from, to }: { from: string, to: string }, userId?: string): Promise<ReportData> => {
  if (userId) {
    await setUserContext(userId);
  }
    // Get both dehacking and setting production entries for complete factory output
    const [dehackingResult, settingResult] = await Promise.all([
        supabase.from('dehacking_entries')
            .select(`*, management_brick_types(name, category, bricks_per_pallet)`)
            .gte('date', from)
            .lte('date', to),
        supabase.from('setting_production_entries')
            .select(`*, management_brick_types(name, category, bricks_per_pallet)`)
            .gte('date', from)
            .lte('date', to)
    ]);

    if (dehackingResult.error) throw dehackingResult.error;
    if (settingResult.error) throw settingResult.error;

    // Combine both types of entries for complete factory output
    const allEntries = [
        ...(dehackingResult.data || []),
        ...(settingResult.data || [])
    ];

    const byDay = allEntries.reduce((acc, entry) => {
        const day = format(new Date(entry.date), 'yyyy-MM-dd');
        if (!acc[day]) acc[day] = { name: format(new Date(entry.date), 'EEE'), production: 0, target: 50000 };
        const brickType = entry.management_brick_types as { bricks_per_pallet: number };
        acc[day].production += entry.pallet_count * (brickType?.bricks_per_pallet || 0);
        return acc;
    }, {} as Record<string, { name: string, production: number, target: number, efficiency?: number }>);

    Object.values(byDay).forEach(day => {
        day.efficiency = day.target > 0 ? Math.round((day.production / day.target) * 100) : 0;
    });

    const byCategory = allEntries.reduce((acc, entry) => {
        const brickType = entry.management_brick_types as { category: string, bricks_per_pallet: number };
        if (!brickType || !brickType.category) return acc;
        if (!acc[brickType.category]) acc[brickType.category] = { category: brickType.category, quantity: 0 };
        acc[brickType.category].quantity += entry.pallet_count * (brickType.bricks_per_pallet || 0);
        return acc;
    }, {} as Record<string, { category: string, quantity: number }>);

    return { main: Object.values(byDay), secondary: Object.values(byCategory) };
};
