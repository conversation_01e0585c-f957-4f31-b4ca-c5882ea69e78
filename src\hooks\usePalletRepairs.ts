import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAllPalletRepairs, createPalletRepair, updatePalletRepair, deletePalletRepair, CreatePalletRepairInput, UpdatePalletRepairInput } from "@/data/palletRepairsData";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";
import { toast } from "sonner";

export function usePalletRepairs() {
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useQuery({
    queryKey: ["pallet-repairs"],
    queryFn: async () => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getAllPalletRepairs(userId || undefined);
    },
  });
}

export function useCreatePalletRepair() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (input: Omit<CreatePalletRepairInput, 'userId'>) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      return createPalletRepair({
        ...input,
        userId,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pallet-repairs"] });
      toast.success("Pallet repair entry recorded successfully!");
    },
    onError: (error: any) => {
      toast.error("Could not record pallet repair entry: " + error?.message);
    },
  });
}

export function useUpdatePalletRepair() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (input: Omit<UpdatePalletRepairInput, 'userId'>) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      return updatePalletRepair({
        ...input,
        userId,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pallet-repairs"] });
      toast.success("Pallet repair entry updated successfully!");
    },
    onError: (error: any) => {
      toast.error("Could not update pallet repair entry: " + error?.message);
    },
  });
}

export function useDeletePalletRepair() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  return useMutation({
    mutationFn: async (id: string) => {
      // Get effective user ID
      const userId = getEffectiveUserId(currentUser, userContextUser);
      if (!userId) {
        throw new Error("User not authenticated");
      }

      return deletePalletRepair(id, userId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pallet-repairs"] });
      toast.success("Pallet repair entry deleted successfully!");
    },
    onError: (error: any) => {
      toast.error("Could not delete pallet repair entry: " + error?.message);
    },
  });
}
