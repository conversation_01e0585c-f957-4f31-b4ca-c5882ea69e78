
import { supabase, setUserContext } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Data for displaying in tables
export interface PalletMovement {
  id: string;
  delivery_date: string;
  delivery_note: string;
  vehicle_registration: string;
  product_type: string;
  destination: string;
  pallets_loaded: number;
  status: PalletStatus;
  client_name: string;
  comments: string | null;
  created_at: string;
  returns: PalletReturn[];
}

export type PalletStatus =
  | "In Transit"
  | "Delivered"
  | "Pending Return"
  | "Returned";

export interface PalletReturn {
  id: string;
  pallet_movement_id: string;
  return_date: string;
  pallets_returned: number;
  condition: string;
  comments: string | null;
  created_at: string;
}

// Get all movements and returns
export async function getAllPalletMovements(userId?: string): Promise<PalletMovement[]> {
  // Set user context if provided
  if (userId) {
    await setUserContext(userId);
  }

  const { data, error } = await supabase
    .from("pallet_movements")
    .select(`
      *,
      returns:pallet_returns (*)
    `)
    .order('delivery_date', { ascending: false });

  if (error) throw new Error(error.message);
  return data ? (data as any) : [];
}

// Dashboard stats - This function is unused and causes a build error as the `pallet_dashboard_stats`
// RPC is not present in the auto-generated Supabase types. Commenting out until fixed.
/*
export async function getPalletDashboardSummary() {
  const { data, error } = await supabase.rpc('pallet_dashboard_stats');
  if (error) throw new Error(error.message);
  return data;
}
*/

// Create outgoing
export async function createPalletMovement(input: {
  delivery_date: string;
  delivery_note: string;
  vehicle_registration: string;
  product_type: string;
  destination: string;
  pallets_loaded: number;
  client_name: string;
  comments?: string;
  userId?: string;
}) {
  // Set user context if provided
  if (input.userId) {
    await setUserContext(input.userId);
  }

  const { data, error } = await supabase
    .from("pallet_movements")
    .insert({
      ...input,
      status: "In Transit",
    })
    .select()
    .maybeSingle();
  return { data, error };
}

// Create return record, and optionally update movement status
export async function recordPalletReturn(input: {
  pallet_movement_id: string;
  return_date: string;
  pallets_returned: number;
  condition: string;
  comments?: string;
  userId?: string;
}) {
  // Set user context if provided
  if (input.userId) {
    await setUserContext(input.userId);
  }

  const { data, error } = await supabase
    .from("pallet_returns")
    .insert({
      ...input,
    })
    .select()
    .maybeSingle();
  return { data, error };
}

type PalletMovementUpdate = Database["public"]["Tables"]["pallet_movements"]["Update"];

// Update status helper
// Accept PalletStatus for type safety
export async function updatePalletMovementStatus(
  id: string,
  status: PalletStatus,
  userId?: string
) {
  // Set user context if provided
  if (userId) {
    await setUserContext(userId);
  }

  const payload: PalletMovementUpdate = { status };
  await supabase
    .from("pallet_movements")
    .update(payload)
    .eq("id", id);
}
