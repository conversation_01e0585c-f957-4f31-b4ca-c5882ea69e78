import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Archive, Plus, Search, Loader2, Edit, FileText, Package } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";
import AddBrickTypeDialog from "@/components/dashboard/AddBrickTypeDialog";
import { EditBrickTypeDialog } from "@/components/dashboard/EditBrickTypeDialog";
import StockManagementDialog from "@/components/dashboard/StockManagementDialog";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { useUser } from "@/contexts/UserContext";
import { getEffectiveUserId } from "@/hooks/useUsers";

export const BrickTypesPage = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isStockDialogOpen, setIsStockDialogOpen] = useState(false);
  const [selectedBrickType, setSelectedBrickType] = useState<ManagementBrickType | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();
  const { currentUser: userContextUser } = useUser();

  const { data: brickTypes = [], isLoading, isError } = useQuery<ManagementBrickType[]>({
    queryKey: ['managementBrickTypes'],
    queryFn: async () => {
      // Get effective user ID and pass to data function
      const userId = getEffectiveUserId(currentUser, userContextUser);
      return getManagementBrickTypes(userId || undefined);
    },
  });

  const activeTypes = brickTypes.filter(type => type.status === 'Active').length;
  // Note: Low Stock and Out of Stock statuses are not in the DB schema yet.
  // We can add them later. For now, we'll display 0.

  // When a new brick type is created, refresh the data
  const handleBrickTypeCreated = () => {
    queryClient.invalidateQueries({ queryKey: ["managementBrickTypes"] });
  };

  // Filter brick types based on search term
  const filteredBrickTypes = brickTypes.filter(type =>
    type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    type.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    type.grade.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Action handlers
  const handleEdit = (brickType: ManagementBrickType) => {
    setSelectedBrickType(brickType);
    setIsEditDialogOpen(true);
  };

  const handleViewSpecs = (brickType: ManagementBrickType) => {
    toast.info(`Specifications for "${brickType.name}":
    • Category: ${brickType.category}
    • Grade: ${brickType.grade}
    • Setting Rate: ₹${brickType.setting_rate}
    • Dehacking Rate: ₹${brickType.dehacking_rate}
    • Overtime Rate: ₹${brickType.overtime_rate}
    • Status: ${brickType.status}`);
  };

  const handleManageStock = (brickType: ManagementBrickType) => {
    setSelectedBrickType(brickType);
    setIsStockDialogOpen(true);
  };

  const handleExportData = () => {
    if (filteredBrickTypes.length === 0) {
      toast.error("No data to export");
      return;
    }

    // Create CSV content
    const headers = ['Name', 'Category', 'Grade', 'Setting Rate', 'Dehacking Rate', 'Overtime Rate', 'Status'];
    const csvContent = [
      headers.join(','),
      ...filteredBrickTypes.map(type => [
        `"${type.name}"`,
        `"${type.category}"`,
        `"${type.grade}"`,
        type.setting_rate,
        type.dehacking_rate,
        type.overtime_rate,
        `"${type.status}"`
      ].join(','))
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `brick-types-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success(`Exported ${filteredBrickTypes.length} brick types to CSV`);
  };

  const handleFilterByCategory = () => {
    toast.info("Category filter will be implemented soon.");
    // TODO: Implement category filter dropdown
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Brick Types Management</h1>
          <p className="text-slate-600">Manage brick types and specifications</p>
        </div>
        <Button className="bg-slate-800 hover:bg-slate-700" onClick={() => setIsDialogOpen(true)}>
          <Plus size={20} className="mr-2" />
          Add Brick Type
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-slate-800">{isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : brickTypes.length}</div>
            <p className="text-slate-600">Total Types</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-green-600">{isLoading ? <Loader2 className="h-6 w-6 animate-spin" /> : activeTypes}</div>
            <p className="text-slate-600">Active</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-yellow-600">0</div>
            <p className="text-slate-600">Low Stock</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-red-600">0</div>
            <p className="text-slate-600">Out of Stock</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Archive size={20} />
            Brick Specifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="relative flex-1">
              <Search size={16} className="absolute left-3 top-3 text-slate-400" />
              <Input
                placeholder="Search brick types..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline" onClick={handleFilterByCategory}>Filter by Category</Button>
            <Button variant="outline" onClick={handleExportData}>Export Data</Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Brick Name</th>
                  <th className="text-left py-3 px-4">Category</th>
                  <th className="text-left py-3 px-4">Grade</th>
                  <th className="text-left py-3 px-4">Setting Rate (₹)</th>
                  <th className="text-left py-3 px-4">Dehacking Rate (₹)</th>
                  <th className="text-left py-3 px-4">Overtime Rate (₹)</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={8} className="text-center py-12">
                      <Loader2 className="h-6 w-6 animate-spin inline-block" />
                    </td>
                  </tr>
                ) : isError ? (
                  <tr>
                    <td colSpan={8} className="text-center py-12 text-red-500">
                      Failed to load brick types.
                    </td>
                  </tr>
                ) : filteredBrickTypes.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="text-center py-12 text-slate-500">
                      {searchTerm ? `No brick types found matching "${searchTerm}".` : "No brick types found."}
                    </td>
                  </tr>
                ) : (
                  filteredBrickTypes.map((type) => (
                    <tr key={type.id} className="border-b hover:bg-slate-50">
                      <td className="py-3 px-4 font-medium">{type.name}</td>
                      <td className="py-3 px-4">{type.category}</td>
                      <td className="py-3 px-4">{type.grade}</td>
                      <td className="py-3 px-4">{type.setting_rate}</td>
                      <td className="py-3 px-4">{type.dehacking_rate}</td>
                      <td className="py-3 px-4">{type.overtime_rate}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          type.status === 'Active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {type.status}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(type)}
                            className="flex items-center gap-1"
                          >
                            <Edit size={14} />
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewSpecs(type)}
                            className="flex items-center gap-1"
                          >
                            <FileText size={14} />
                            Specs
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleManageStock(type)}
                            className="flex items-center gap-1"
                          >
                            <Package size={14} />
                            Stock
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Add New Brick Type Dialog */}
      <AddBrickTypeDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSuccess={handleBrickTypeCreated}
      />

      {/* Edit Brick Type Dialog */}
      <EditBrickTypeDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        brickType={selectedBrickType}
        onSave={handleBrickTypeCreated}
      />

      {/* Stock Management Dialog */}
      <StockManagementDialog
        open={isStockDialogOpen}
        onOpenChange={setIsStockDialogOpen}
        brickType={selectedBrickType}
      />
    </div>
  );
};
