
import { supabase } from '@/integrations/supabase/client';

export interface AuditLogEntry {
  action: string;
  tableName?: string;
  recordId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  userId?: string;
}

// Log an audit event
export const logAuditEvent = async (entry: AuditLogEntry): Promise<void> => {
  try {
    const { error } = await supabase
      .from('audit_logs')
      .insert({
        user_id: entry.userId || null,
        action: entry.action,
        table_name: entry.tableName || null,
        record_id: entry.recordId || null,
        old_values: entry.oldValues || null,
        new_values: entry.newValues || null,
        ip_address: null, // Could be enhanced to capture real IP
        user_agent: navigator.userAgent
      });

    if (error) {
      console.error('Failed to log audit event:', error);
    }
  } catch (error) {
    console.error('Audit logging error:', error);
  }
};

// Helper function to log authentication events
export const logAuthEvent = async (action: 'login' | 'logout' | 'login_failed', userId?: string, details?: Record<string, any>): Promise<void> => {
  await logAuditEvent({
    action: `auth_${action}`,
    userId,
    newValues: details
  });
};

// Helper function to log data modifications
export const logDataChange = async (
  action: 'create' | 'update' | 'delete',
  tableName: string,
  recordId: string,
  oldValues?: Record<string, any>,
  newValues?: Record<string, any>,
  userId?: string
): Promise<void> => {
  await logAuditEvent({
    action: `${tableName}_${action}`,
    tableName,
    recordId,
    oldValues,
    newValues,
    userId
  });
};
