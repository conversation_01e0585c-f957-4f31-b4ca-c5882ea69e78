
import React, { useState } from 'react';
import { LoadPlanningEntry, ViewMode } from '@/types/loadPlanning';
import { DailyView } from './DailyView';
import { WeeklyViewNew } from './WeeklyViewNew';
import { MonthlyView } from './MonthlyView';
import { EditLoadDialog } from './EditLoadDialog';
import { RescheduleLoadDialog } from './RescheduleLoadDialog';
import { useLoadPlanning } from '@/hooks/useLoadPlanning';

interface LoadsListProps {
  loads: LoadPlanningEntry[];
  isLoading: boolean;
  viewMode: ViewMode;
  currentDate: Date;
}

export const LoadsList: React.FC<LoadsListProps> = ({
  loads,
  isLoading,
  viewMode,
  currentDate
}) => {
  const [editingLoad, setEditingLoad] = useState<LoadPlanningEntry | null>(null);
  const [reschedulingLoad, setReschedulingLoad] = useState<LoadPlanningEntry | null>(null);
  
  const {
    markAsDispached,
    markAsReady,
    cancelLoad,
    isMarkingDispatched,
    isMarkingReady,
    isCancelling
  } = useLoadPlanning();

  const handleEditLoad = (load: LoadPlanningEntry) => {
    setEditingLoad(load);
  };

  const handleRescheduleLoad = (load: LoadPlanningEntry) => {
    setReschedulingLoad(load);
  };

  const handleAddLoad = (selectedDate?: Date) => {
    // This functionality can be added if needed
    console.log('Add load for date:', selectedDate);
  };

  const renderView = () => {
    const commonProps = {
      loads,
      currentDate,
      isLoading,
      onEditLoad: handleEditLoad,
      onRescheduleLoad: handleRescheduleLoad,
      onMarkAsReady: markAsReady,
      onMarkAsDispatched: markAsDispached,
      onCancelLoad: cancelLoad,
      isMarkingReady,
      isMarkingDispatched: isMarkingDispatched || isCancelling
    };

    switch (viewMode) {
      case 'daily':
        return <DailyView {...commonProps} />;
      case 'weekly':
        return <WeeklyViewNew {...commonProps} />;
      case 'monthly':
        return <MonthlyView {...commonProps} onAddLoad={handleAddLoad} />;
      default:
        return <DailyView {...commonProps} />;
    }
  };

  return (
    <>
      {renderView()}
      
      {editingLoad && (
        <EditLoadDialog
          load={editingLoad}
          open={!!editingLoad}
          onOpenChange={(open) => !open && setEditingLoad(null)}
        />
      )}

      {reschedulingLoad && (
        <RescheduleLoadDialog
          load={reschedulingLoad}
          open={!!reschedulingLoad}
          onOpenChange={(open) => !open && setReschedulingLoad(null)}
        />
      )}
    </>
  );
};
