
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useAIInsights = (type: string) => {
  return useQuery({
    queryKey: ['ai-insights', type],
    queryFn: async () => {
      console.log('Calling AI Business Intelligence function for type:', type);
      
      const { data, error } = await supabase.functions.invoke('ai-business-intelligence', {
        body: { 
          type,
          params: {}
        }
      });

      if (error) {
        console.error('Error calling AI function:', error);
        throw error;
      }

      return data?.data || {
        keyMetrics: [],
        alerts: [],
        recommendations: []
      };
    },
    refetchInterval: 300000, // Refetch every 5 minutes for live data
  });
};
