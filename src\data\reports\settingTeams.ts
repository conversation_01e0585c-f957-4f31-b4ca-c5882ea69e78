
import { supabase, setUserContext } from "@/integrations/supabase/client";
import { getTeamsWithMembers } from "../teamData";
import type { ReportData } from "./types";

export const getSettingTeamsReport = async ({ from, to }: { from: string, to: string }, userId?: string): Promise<ReportData> => {
  if (userId) {
    await setUserContext(userId);
  }

  const { data: entries, error } = await supabase
    .from('setting_production_entries')
    .select(`*, teams(name), management_brick_types(bricks_per_pallet)`)
    .gte('date', from)
    .lte('date', to);
  if (error) throw error;

  const teamsWithMembers = await getTeamsWithMembers();

  const byTeam = entries.reduce((acc, entry) => {
    const team = entry.teams as { name: string };
    const brickType = entry.management_brick_types as { bricks_per_pallet: number };
    if (!team) return acc;
    if (!acc[entry.team_id]) acc[entry.team_id] = { name: team.name, bricks_set: 0, target: 200000 };
    acc[entry.team_id].bricks_set += entry.pallet_count * (brickType?.bricks_per_pallet || 0);
    return acc;
  }, {} as Record<string, { name: string, bricks_set: number, target: number, efficiency?: number }>);
  
  Object.values(byTeam).forEach(team => {
      team.efficiency = team.target > 0 ? Math.round((team.bricks_set / team.target) * 100) : 0;
  });

  const secondary = teamsWithMembers.map(team => ({
      team: team.name,
      members: team.members.length,
  }));

  return { main: Object.values(byTeam), secondary };
};
