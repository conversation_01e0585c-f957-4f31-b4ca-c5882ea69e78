import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AIOverviewDashboard } from '@/components/ai-insights/AIOverviewDashboard';
import { ProductionForecasting } from '@/components/ai-insights/ProductionForecasting';
import { PerformanceAnalytics } from '@/components/ai-insights/PerformanceAnalytics';
import { AIAssistantChat } from '@/components/ai-insights/AIAssistantChat';
import { AlertCircle, TrendingUp, Users, MessageSquare } from 'lucide-react';

export const AIInsightsPage = () => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold text-foreground">AI Business Intelligence</h1>
        <p className="text-muted-foreground">
          AI-powered insights, forecasting, and recommendations for your brick manufacturing operations
        </p>
      </div>

      {/* Warning Card for Beta Feature */}
      <Card className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
        <CardContent className="flex items-center gap-3 pt-6">
          <AlertCircle className="h-5 w-5 text-amber-600" />
          <div>
            <p className="text-sm font-medium text-amber-800 dark:text-amber-200">
              Beta Feature - AI insights are generated from your existing production data
            </p>
            <p className="text-sm text-amber-700 dark:text-amber-300">
              Recommendations should be reviewed by management before implementation
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="forecasting" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Forecasting
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="assistant" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            AI Assistant
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <AIOverviewDashboard />
        </TabsContent>

        <TabsContent value="forecasting" className="space-y-6">
          <ProductionForecasting />
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <PerformanceAnalytics />
        </TabsContent>

        <TabsContent value="assistant" className="space-y-6">
          <AIAssistantChat />
        </TabsContent>
      </Tabs>
    </div>
  );
};